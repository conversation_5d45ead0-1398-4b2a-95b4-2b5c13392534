<view><view class="sdd"></view><block wx:for="{{list}}" wx:for-item="c" wx:for-index="k"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({c})}}" class="item" bindtap="__e"><view class="stt"><block wx:if="{{c.ydzt=='2'}}"><image class="wei" src="/static/wei.png"></image></block><block wx:if="{{c.ydzt=='1'}}"><image class="wei" src="/static/yi.png"></image></block><text class="text1">{{c.bt}}</text></view><view class="sbb"><image class="r" src="/static/15.png"></image><text>{{c.optdt}}</text></view></view></block><view class="cop">{{nodata}}</view></view>