page {
	font-family: '<PERSON>Fang'; 
	background-color: #f6f7fd;
}
.navs{
	margin-top: 20rpx;
	width: 100%;
	margin-left: 20rpx;
	overflow-x: scroll;
	white-space: nowrap;
	text{
		display: inline-block;
		white-space: nowrap;
		box-sizing:border-box;
		padding: 8rpx 20rpx;
		background-color: #FFFFFF;
		font-weight: 500;
		font-size: 28upx;
		color: #555555;
		border-radius: 10rpx;
		margin-right: 30upx;
		&.act{
			color: #017BFF;
			background: rgba(1,123,255,0.12);
			border: 2rpx solid #017BFF;
		}
	}
}
.box{
	margin-top: 30rpx;
	width: 89%;
	margin-left: 3%;
	background-color: #fff;
	border-radius: 20upx;
	padding: 20upx;
	box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(0,0,0,0.05);
	
	.text1{
		display: block;
		width: 100%;
		font-weight: 500;
		font-size: 30upx;
		color: #333333;
	}
	.sf{
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-top: 30upx;
		.bt{
			color: #017BFF;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			font-size: 20upx;
			font-size: 24rpx;
			font-weight: 500;
			padding: 8rpx 20rpx;
			box-sizing: border-box;
			background: rgba(1,123,255,0.12);
		}
		.sft{
			display: flex;
			color: #999999;
			font-size: 24rpx;
			.sdd{
				display: flex;
				align-items: center;
				margin-left: 20rpx;
				
				text{
					padding-left: 10upx;
				}
				&:last-of-type{
					padding-left: 10upx;
				}
			}
			
			.sdd:first-child {
				
				image {
					width: 40rpx;
					height: 40rpx;
				}
			}
		}
	}
	
	image {
		width: 30rpx;
		height: 30rpx;
	}
}
/**弹窗***/
.mack{
	width: 100%;
	height: 100vh;
	z-index: 12;
	position: fixed;
	bottom: 0;
	background-color: rgba(#000, .42);
}
.shimw{
	width: 100%;
	overflow: hidden;
	z-index: 15;
	min-height: 30vh;
	position: fixed;
	top: 0;
	background-color: #fff;
	border-radius: 0upx 0upx 20upx 20upx;
	.stt{
		width: 160upx;
		height: 52upx;
		background-color: rgba(#499EFB, .72);
		font-size: 28upx;
		display: flex;
		color: #fff;
		align-items: center;
		border-radius: 40upx;
		padding: 2upx 16upx;
		justify-content: center;
		margin-top: 20upx;
		margin-left: 20upx;
		text{
			padding-right: 10upx;
		}
	}
	.xxx{
		font-size: 28upx;
		color: #333;
		margin-top: 30upx;
		.sright{
			width: 75%;
			float: right;
			position: relative;
			height: 1000upx;
			.box1{
				max-height: 850upx;
				overflow-y: scroll;
				padding: 0upx 20upx;
				.item{
					margin-bottom: 30upx;
					.stt2{
						color: #000;
						font-weight: bold;
						padding-bottom: 20upx;
						font-size: 24upx !important;
					}
					.ul{
						font-size: 22rpx;
						overflow: hidden;
						text{
							display: inline-block;
							width: 31%;
							float: left;
							background-color: #F5F7FB;
							text-align: center;
							margin-bottom: 30upx;
							padding: 15rpx 0upx;
							border-radius: 100rpx;
							margin-right: 2.3%;
							font-weight: 500;
							
							&.on{
								background: #D6EAFE;
								color: #2D9AFE;
							}
						}
					}
				}
			}
			.butt{
				position: absolute;
				bottom: 30upx;
				background-color: #499EFB;
				width: 70%;
				margin-left: 50%;
				transform: translateX(-50%);
				display: flex;
				font-size: 28upx;
				justify-content: center;
				align-items: center;
				color: #fff;
				height: 70upx;
				border-radius: 50upx;
			}
		}
		.left{
			width: 25%;
			background-color: #F5F7FB;
			float: left;
			height: 1000upx;
			position: relative;
			font-size: 28upx;
			.items{
				max-height: 850upx;
				overflow-y: scroll;
				text{
					display: block;
					padding-left: 30upx;
					line-height: 90upx;
					&.act{
						background-color: #fff;
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 28rpx;
						color: #2D9AFE;
						position: relative;
						
						&::before{
							content: "";
							position: absolute;
							width: 8upx;
							height: 40rpx;
							background-color: rgba(#499EFB, 1);
							left: 0;
							top: 30%;
							border-radius: 20upx;
						}
					}
				}
			}
			.fb{
				position: absolute;
				bottom: 50upx;
				width: 100%;
				left: 0upx;
				text-align: center;
			}
		}
	}
}
/**弹窗***/