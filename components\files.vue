<template>
	<view class="files" v-if="files && files.length > 0">
		<view class="item" v-for="(c,k) in files">
			<view class="sleft">
				<image src="/static/pdf1.png" v-if="c.fileext == 'pdf'"></image>
				<image src="/static/word1.png" v-else-if="c.fileext == 'doc' || (c.fileext == 'docx')"></image>
				<image src="/static/qt1.png" v-else></image>
			</view>
			<view class="cen">
				<text class="text1">{{c.filename}}</text>
				<text class="size">{{c.filesizecn}}</text>
			</view>
			<view class="srg" @click="down(c.filepath)" style="transform: translateX(60upx);">
				<image src="@/static/Frame11.png"></image>
			</view>
		</view>
		
	</view>
</template>

<script>
	export default {
		name: "files",
		props: {
			files: {
				type: Array,
				default: []
			}
		},
		data() {
			return {

			};
		},
		methods: {
			down(file) {
				if (!file) {
					return false;
				}
				console.log(file);
				var a = file.split('.');
				if (a[a.length - 1] == 'zip') {
					uni.setClipboardData({
						data:file,
						success: () =>{
							uni.getClipboardData({
								success: (res) => {
									uni.showToast({
										icon:"none",
										title: "复制成功,请打开浏览器进行粘贴下载！",
									});
								}
							});
						}
					});


				} else {
					location.href = file;
				}

			}
		}
	}
</script>

<style lang="scss" scoped>
	.files {
		background: #FFFFFF;
		border-radius: 10px 10px 10px 10px;
		width: 93%;
		margin-left: 2%;
		padding: 10upx;
		margin-top: 30upx;
		.item {
			display: flex;
			padding-bottom: 20upx;
			border-bottom: 1px solid rgba(0, 0, 0, 0.05);
			margin-bottom: 20upx;

			&:last-of-type {
				border-bottom: none;
				padding-bottom: 0upx;
			}

			.sleft {
				image {
					width: 64upx;
					height: 64upx;
					
				}
			}

			.cen {
				padding-left: 20upx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28upx;
				color: #333333;
				width: 70%;

				text {
					display: block;

					&:last-of-type {
						font-size: 24upx;
						color: #999999;
						padding-top: 10upx;
					}
				}


			}

			.srg {
				display: flex;
				align-items: center;
				justify-content: center;
				image{
					width: 50upx;
					height: 50upx;
				}
			}
		}
	}
</style>