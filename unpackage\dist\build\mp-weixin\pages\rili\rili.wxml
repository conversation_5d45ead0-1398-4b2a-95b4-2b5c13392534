<view><rili bind:childEvent="__e" vue-id="5b1eb780-1" data-event-opts="{{[['^childEvent',[['handleChildEvent']]]]}}" bind:__l="__l"></rili><view class="bpox"><view class="stt1"><image src="/static/<EMAIL>"></image><text style="font-size:32rpx;">{{(day||"今日")+" 考试"}}</text></view><block wx:for="{{kaoshi}}" wx:for-item="c" wx:for-index="k"><view class="item"><view class="sttt">{{c.bt}}</view><view class="cont">{{''+c.nr+''}}</view></view></block><block wx:if="{{$root.g0}}"><view class="search-nodata f-d-c-c data-v-56a7e7a2"><u-empty vue-id="5b1eb780-2" mode="data" text="暂无记录" icon="http://cdn.uviewui.com/uview/empty/data.png" bind:__l="__l"></u-empty></view></block></view><view class="cop"></view><footer vue-id="5b1eb780-3" act="rili" bind:__l="__l"></footer></view>