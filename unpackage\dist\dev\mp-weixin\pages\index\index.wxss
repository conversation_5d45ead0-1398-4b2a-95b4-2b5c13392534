@charset "UTF-8";
.top {
  background: linear-gradient(180deg, #E9EDFE 0%, #F5F7FB 100%);
  width: 100%;
  height: 130rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.top .left {
  width: 30%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.top .left image {
  width: 180rpx;
  height: 70rpx;
}
.top .right .inpuit {
  background-color: #fff;
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  width: 432rpx;
  padding: 15rpx 20rpx;
  border-radius: 50rpx;
}
.top .right .inpuit text {
  color: #cbc9c9;
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
  padding-left: 20rpx;
  font-size: 28rpx;
}
.top .right image {
  width: 45rpx;
  height: 45rpx;
  -webkit-transform: translateY(4rpx);
          transform: translateY(4rpx);
}
 uni-swiper .uni-swiper-dots-horizontal {
  position: absolute;
  bottom: 20rpx;
  /* 调整指示点到底部的距离 */
  left: 880rpx !important;
  /* 默认居中，可以通过transform调整具体位置 */
  width: 100%;
  /* 根据需要调整宽度 */
  display: flex;
  /* 使用flex布局来排列指示点 */
}
 uni-swiper .uni-swiper-dot-active {
  background-color: #fff;
  /* 选中时的颜色 */
  width: 30rpx;
  height: 11rpx;
  border-radius: 10rpx;
  margin-top: 3rpx;
}
 .u-notice-bar .u-notice__content__text {
  font-weight: normal !important;
  font-size: 28rpx !important;
}
.focus {
  padding-bottom: 10rpx;
  width: 100%;
  background: #fff !important;
  background: linear-gradient(to bottom, #c7dffd, #fff);
}
.focus .focus2 {
  width: 95%;
  margin-left: 2%;
  position: relative;
}
.focus .focus2 .screen-swiper {
  width: 100%;
  height: 275rpx;
  overflow: hidden;
  border-radius: 20rpx;
  position: relative;
}
.focus .focus2 .screen-swiper image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx !important;
}
.wap {
  background: #f4f6ff;
  padding-top: 10rpx;
  overflow: hidden;
}
.laba {
  display: flex;
  align-items: center;
  background: #FFFFFF;
  width: 95%;
  margin-left: 2.5%;
  height: 35rpx;
  border-radius: 20rpx;
  padding: 20rpx 0rpx;
  margin-top: 10rpx 0rpx;
}
.laba .slef {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  box-sizing: border-box;
}
.laba image:first-of-type {
  width: 50rpx;
  height: 50rpx;
  margin-right: 5rpx;
}
.laba image:last-of-type {
  width: 50rpx;
  height: 55rpx;
  margin-right: 5rpx;
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
}
.laba .acd {
  font-size: 20rpx;
  display: inline-block;
  padding-left: 10rpx;
  width: 100%;
  -webkit-transform: translateY(2);
          transform: translateY(2);
}
.navs {
  background: #f4f6ff;
  width: 100%;
  overflow: hidden;
  padding-top: 5rpx;
  font-family: "Alibaba";
  padding-bottom: 10rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.navs .item {
  width: 25%;
  text-align: center;
  margin: 20rpx 0rpx;
}
.navs .item image {
  width: 100rpx;
  height: 100rpx;
}
.navs .item text {
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  display: block;
  color: #333333;
  font-size: 24rpx;
  margin-top: -5rpx;
}
.adv {
  width: 100%;
  margin-top: 20rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
}
.adv image {
  width: 49%;
  height: 180rpx;
  border-radius: 20rpx;
}
.adv image:last-of-type {
  -webkit-transform: translateX(10rpx);
          transform: translateX(10rpx);
}
.adv3 {
  width: 100%;
  margin-top: 30rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
}
.adv3 image {
  width: 100%;
  height: 228rpx;
  border-radius: 20rpx;
}
.items {
  width: 100%;
  padding: 0 20rpx;
  box-sizing: border-box;
  margin-top: 20rpx;
}
.items .background {
  width: 100%;
  border-radius: 20rpx;
}
.items .background .stitle {
  position: relative;
}
.items .background .stitle image {
  width: 100%;
  height: 100rpx;
  vertical-align: middle;
}
.items .background .stitle .sleft {
  font-size: 30rpx;
  position: absolute;
  top: 22rpx;
  left: 30rpx;
  color: #fff;
  font-size: 34rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
}
.items .background .stitle .sright {
  display: flex;
  align-items: center;
  position: absolute;
  top: 40rpx;
  right: 20rpx;
  color: #869198;
  font-size: 24rpx;
}
.items .background .stitle .sright text {
  margin-right: 5rpx;
}
.items .background .box1 {
  width: 100%;
  border-radius: 0 0 20rpx 20rpx;
  box-sizing: border-box;
  box-shadow: 4rpx 4rpx 4rpx 4rpx rgba(0, 0, 0, 0.03);
}
.items .background .box1 .ul {
  background: linear-gradient(#A3D2FE 0%, #FFFFFF);
  padding: 20rpx 15rpx;
  overflow-x: scroll;
  box-sizing: border-box;
  white-space: nowrap;
  width: 100%;
  overflow: auto;
}
.items .background .box1 .ul text {
  display: inline-block;
  white-space: nowrap;
  box-sizing: border-box;
  padding: 8rpx 20rpx;
  color: #999999;
  font-size: 24rpx;
  font-weight: 400;
  border: 2rpx solid #fff;
  background-color: #fff;
  border-radius: 10rpx;
  margin-right: 30rpx;
}
.items .background .box1 .ul text.act {
  background: rgba(45, 154, 254, 0.12);
  color: #2D9AFE;
  border: 2rpx solid #2D9AFE;
}
.items .backgrounds {
  width: 100%;
  border-radius: 20rpx;
}
.items .backgrounds .stitle {
  position: relative;
}
.items .backgrounds .stitle image {
  width: 100%;
  height: 100rpx;
  vertical-align: middle;
}
.items .backgrounds .stitle .sleft {
  font-size: 30rpx;
  position: absolute;
  top: 22rpx;
  left: 30rpx;
  color: #fff;
  font-size: 34rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
}
.items .backgrounds .stitle .sright {
  display: flex;
  align-items: center;
  position: absolute;
  top: 40rpx;
  right: 20rpx;
  color: #869198;
  font-size: 24rpx;
}
.items .backgrounds .stitle .sright text {
  margin-right: 5rpx;
}
.items .backgrounds .box1 {
  width: 100%;
  border-radius: 0 0 20rpx 20rpx;
  padding: 0 0 10rpx;
  box-sizing: border-box;
  box-sizing: border-box;
  box-shadow: 4rpx 4rpx 4rpx 4rpx rgba(0, 0, 0, 0.03);
}
.items .backgrounds .box1 .ul {
  background: linear-gradient(#FDE3B2 0%, #FFFFFF);
  padding: 20rpx 15rpx;
  overflow-x: scroll;
  box-sizing: border-box;
  white-space: nowrap;
  width: 100%;
  overflow: auto;
}
.items .backgrounds .box1 .ul text {
  display: inline-block;
  white-space: nowrap;
  box-sizing: border-box;
  padding: 8rpx 20rpx;
  color: #999999;
  font-size: 24rpx;
  font-weight: 400;
  border: 2rpx solid #fff;
  background-color: #fff;
  border-radius: 10rpx;
  margin-right: 30rpx;
}
.items .backgrounds .box1 .ul text.act {
  background: rgba(254, 192, 76, 0.12);
  color: #FEC04C;
  border: 2rpx solid #FEC04C;
}
.items .backgroundss {
  width: 100%;
  border-radius: 20rpx;
}
.items .backgroundss .stitle {
  position: relative;
}
.items .backgroundss .stitle image {
  width: 100%;
  height: 100rpx;
  vertical-align: middle;
}
.items .backgroundss .stitle .sleft {
  font-size: 30rpx;
  position: absolute;
  top: 22rpx;
  left: 30rpx;
  color: #fff;
  font-size: 34rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
}
.items .backgroundss .stitle .sright {
  display: flex;
  align-items: center;
  position: absolute;
  top: 40rpx;
  right: 20rpx;
  color: #869198;
  font-size: 24rpx;
}
.items .backgroundss .stitle .sright text {
  margin-right: 5rpx;
}
.items .backgroundss .box1 {
  width: 100%;
  border-radius: 0 0 20rpx 20rpx;
  padding: 0 0 10rpx;
  box-sizing: border-box;
  box-sizing: border-box;
  box-shadow: 4rpx 4rpx 4rpx 4rpx rgba(0, 0, 0, 0.03);
}
.items .backgroundss .box1 .ul {
  background: linear-gradient(#E0F9F9 0%, #FFFFFF);
  padding: 20rpx 15rpx;
  overflow-x: scroll;
  box-sizing: border-box;
  white-space: nowrap;
  width: 100%;
  overflow: auto;
}
.items .backgroundss .box1 .ul text {
  display: inline-block;
  white-space: nowrap;
  box-sizing: border-box;
  padding: 8rpx 20rpx;
  color: #999999;
  font-size: 24rpx;
  font-weight: 400;
  border: 2rpx solid #fff;
  background-color: #fff;
  border-radius: 10rpx;
  margin-right: 30rpx;
}
.items .backgroundss .box1 .ul text.act {
  background: rgba(68, 203, 204, 0.12);
  color: #44CBCC;
  border: 2rpx solid #44CBCC;
}
.giid {
  display: flex;
  margin-top: 20rpx;
  padding: 10rpx 0;
  box-sizing: border-box;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
  border-radius: 0 0 20rpx 20rpx;
}
.giid .giid_item {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 20rpx 10rpx 20rpx 20rpx;
  box-sizing: border-box;
}
.giid image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 20rpx;
}
.giid .slf {
  background-color: #fff;
  width: 440rpx;
  border-radius: 0rpx 20rpx 20rpx 0rpx;
  position: relative;
  padding: 10rpx 5rpx 10rpx 10rpx;
  height: 100%;
  margin-left: 15rpx;
}
.giid text {
  display: block;
}
.giid text.text1 {
  font-family: PingFang SC, PingFang SC;
  font-weight: normal !important;
  font-size: 28rpx;
  width: 100%;
  color: #222222;
}
.giid text.button {
  position: absolute;
  background: linear-gradient(360deg, #2D9AFE 0%, #47B8FF 100%);
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
  border-radius: 30rpx;
  padding: 15rpx 25rpx;
  right: 0;
  bottom: 20rpx;
}
.k3 {
  overflow: hidden;
  box-sizing: border-box;
}
.k3 .item3 {
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
  width: 48.5%;
  float: left;
  border-radius: 20rpx;
  margin-bottom: 15rpx;
  padding: 15rpx 0;
}
.k3 .item3:nth-child(2n) {
  float: right;
}
.k3 .item3 .text2 {
  margin-top: 10rpx;
  padding: 0 20rpx;
  font-weight: bold;
  font-size: 28rpx;
  color: #333333;
  font-family: PingFang SC, PingFang SC;
  line-height: 42rpx;
  font-weight: bold;
}
.k3 .item3 image {
  width: 100%;
  height: 200rpx;
  border-radius: 20rpx;
}
.k3 .item3 .firs {
  padding: 10rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.k3 .item3 .firs .sdd {
  font-size: 26rpx;
  color: #FEC04C;
  font-weight: bold;
}
.k3 .item3 .firs .sdd text:last-of-type {
  font-size: 26rpx;
  color: #999;
  margin-left: 10rpx;
}
.k3 .item3 .btn {
  display: flex;
  justify-content: center;
  align-items: center;
}
.k3 .item3 .btn .bbb {
  width: 200rpx;
  background: linear-gradient(360deg, #2D9AFE 0%, #47B8FF 100%);
  border-radius: 66rpx;
  font-weight: bold;
  font-size: 24rpx;
  color: #FFFFFF;
  padding: 12rpx 26rpx;
  text-align: center;
}
.study {
  padding: 10rpx 20rpx;
  box-sizing: border-box;
}
.study .bf5 {
  background-color: #fff;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  margin-top: 30rpx;
  padding: 20rpx;
  box-sizing: border-box;
  width: 100%;
  font-family: "Alibaba";
}
.study .bf5 .text1 {
  display: block;
  width: 100%;
  font-weight: bold;
  padding: 8rpx 0rpx;
  font-size: 28rpx;
}
.study .bf5 .sf9 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 20rpx;
}
.study .bf5 .sf9 .s11 {
  font-size: 24rpx;
  font-weight: 500;
  color: #222222;
}
.study .bf5 .sf9 .sdd {
  color: #626164;
  display: flex;
  align-items: center;
  font-size: 24rpx;
}
.study .bf5 .sf9 .sdd text {
  display: inline-block;
  padding: 0rpx 10rpx;
}
.mark {
  width: 100%;
  height: 100vh;
  z-index: 12;
  position: fixed;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.42);
  display: flex;
  align-items: center;
  justify-content: center;
}
.mark .box5 {
  background-image: url(../../static/popup.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  width: 520rpx;
  margin-left: 2.2%;
  overflow: hidden;
  z-index: 15;
  min-height: 520rpx;
  position: fixed;
  top: 40%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  border-radius: 20rpx;
}
.mark .box5 .dd5 {
  width: 100%;
  height: 60rpx;
  position: relative;
}
.mark .box5 .close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: linear-gradient(to bottom, #359dff, #84bcfe);
  position: absolute;
  right: 0;
  top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.mark .box5 image {
  width: 300rpx;
  height: 280rpx;
  margin-left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  margin-top: 40rpx;
}
.mark .box5 .text {
  font-weight: bold;
  font-size: 32rpx;
  color: #000;
  text-align: center;
}
.mark .box5 .but {
  font-weight: bold;
  font-size: 30rpx;
  color: #fff;
  text-align: center;
  background-color: #2695ff;
  width: 60%;
  margin-left: 20%;
  margin-top: 50rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 50rpx;
}

