page{
	margin: 0 auto;
	    margin-bottom: 0px;
	  color: #444;
	  overflow-x: hidden;
	  overflow-y: scroll;
	  background: #f2f2f2;
	  position: relative;
}
.top{
	width: 100%;
	position: relative;
	video{
		width: 100%;
		height: 440upx;
	}
}
.recordinfo{
	background: #ffffff;
	padding: 20px 15px 5px 15px;
	margin-bottom: 0px;
	view{
		&:first-of-type{
			font-size: 18px;
			  color: #333333;
			  padding-bottom: 10px;
		}
		&:last-of-type{
			color: #999999;
			  font-size: 12px;
			
		}
	}
}
.chapter_title{
	padding: 15px 15px;
	  margin-bottom: 0px;
	  color: #999999;
	    font-size: 14px;
}
.recordcontent{
	.recordlist{
		background: #ffffff;
		view{
			padding: 15px 15px;
			  border-bottom: 1px solid #e5e5e5;
			  display: flex;
			  align-items: center;
			  font-size: 28upx;
			  image{
				  &:first-of-type{
					  height: 18px;
					  width: 18px;
					    vertical-align: middle;
					    position: relative;
					    top: -1px;
					    margin-right: 10px;
				  }
				  &:last-of-type{
					  width: 13px;
					  height: 13px;
					 position: absolute;
					 right: 40upx;
				  }
			  }
			&.active{
				color: #0095ff;
			}  
		}
	}
}