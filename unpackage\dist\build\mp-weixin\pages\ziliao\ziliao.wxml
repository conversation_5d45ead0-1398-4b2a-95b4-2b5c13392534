<view><view class="navs"><block wx:for="{{cats}}" wx:for-item="c" wx:for-index="k"><text data-event-opts="{{[['tap',[['setc2',['$0','$1'],[[['cats','',k,'name']],[['cats','',k,'tab']]]]]]]}}" class="{{[c.name==cname?'act':'']}}" bindtap="__e">{{c.name}}</text></block></view><block wx:for="{{list}}" wx:for-item="c" wx:for-index="k"><view data-event-opts="{{[['tap',[['tourlIxun',['$0'],[[['list','',k]]]]]]]}}" class="box" bindtap="__e"><text class="text1">{{c.bt}}</text><view class="sf"><text class="bt">{{c.lx}}</text><view class="sft"><view class="sdd"><image src="/static/14.png"></image><text>{{c.views}}</text></view><view class="sdd"><image src="/static/15.png"></image><text>{{c.optdt}}</text></view></view></view></view></block><block wx:if="{{mark}}"><view data-event-opts="{{[['tap',[['setHide']]]]}}" class="mack" bindtap="__e"></view></block><block wx:if="{{mark}}"><view class="shimw"><view class="stt"><text>{{cname}}</text><u-icon vue-id="7b693540-1" name="arrow-right" color="#fff" size="12" bind:__l="__l"></u-icon></view><view class="xxx"><view class="left"><view class="items"><block wx:for="{{cats}}" wx:for-item="c" wx:for-index="k"><text data-event-opts="{{[['tap',[['setC',['$0',k],[[['cats','',k]]]]]]]}}" class="{{[c.name==cname?'act':'']}}" bindtap="__e">{{c.name}}</text></block></view><view data-event-opts="{{[['tap',[['chongzhi']]]]}}" class="fb" bindtap="__e">重置</view></view><view class="sright"><view class="box1"><block wx:for="{{cats}}" wx:for-item="c" wx:for-index="k"><block wx:if="{{cname=='全部分类'}}"><view class="item"><view class="stt2" style="font-size:12px;">{{c.name}}</view><view class="ul"><block wx:for="{{c.child}}" wx:for-item="c2" wx:for-index="k2"><text data-event-opts="{{[['tap',[['setchild',['$0','$1'],[[['cats','',k],['child','',k2,'lx']],[['cats','',k,'tab']]]]]]]}}" class="{{[lx==c2.lx?'on':'']}}" bindtap="__e">{{c2.lx}}</text></block></view></view></block></block><block wx:if="{{cname!='全部分类'}}"><view class="item"><view class="stt2" style="font-size:12px;">{{cname}}</view><view class="ul"><block wx:for="{{cats[ck].child}}" wx:for-item="c2" wx:for-index="k2"><text data-event-opts="{{[['tap',[['setchild',['$0','$1'],[[['cats.'+ck+'.child','',k2,'lx']],'cats.'+ck+'.tab']]]]]}}" class="{{[lx==c2.lx?'on':'']}}" bindtap="__e">{{c2.lx}}</text></block></view></view></block></view><view data-event-opts="{{[['tap',[['butt']]]]}}" class="butt" bindtap="__e"><text>确定</text></view></view></view></view></block><view class="cop">{{nodata}}</view><view class="cop"></view><footer vue-id="7b693540-2" act bind:__l="__l"></footer></view>