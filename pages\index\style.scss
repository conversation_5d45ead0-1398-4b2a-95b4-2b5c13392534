page {
	font-family: '<PERSON>F<PERSON>'; 
}

@font-face {
		font-family: 'AlimamaShuHeiTiBold'; /* 自定义字体名，可以是任意名称 */
		src: url('@/static/AlimamaShuHeiTi-Bold.ttf'); /* 字体文件路径和格式 */
		font-weight: bold; /* 如果字体有特定的粗细，可以指定 */
		// font-style: normal; /* 如果字体有特定的样式，如斜体，可以指定 */
}

.top{
	background: linear-gradient( 180deg, #E9EDFE 0%, #F5F7FB 100%);
	width: 100%;
	height: 130rpx;
	padding: 0 10upx;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	.left{
		width: 30%;
		// margin-top: 30upx;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
		
		image{
			width: 180rpx;
			height: 70rpx;
			// transform: scale(1.1);
		}
	}
	.right{
		// margin-top: 30upx;
		.inpuit{
			background-color: #fff;
			display: flex;
			align-items: center;
			margin-left: 20upx;
			width: 432upx;
			padding: 15rpx 20upx;
			// box-sizing: border-box;
			border-radius: 50upx;
			text{
				color: #cbc9c9;
				transform: translateY(2upx);
				padding-left: 20upx;
				font-size: 28rpx;
			}
		}
		image{
			width: 45rpx;
			height: 45rpx;
			transform: translateY(4upx);
		}
	}
}
// .sdd3{
// 	width: 100%;
// 	height: 20upx;
// 	background: linear-gradient(to bottom, #bfdbfc, #c7dffd);
// }

::v-deep uni-swiper .uni-swiper-dots-horizontal {
	position: absolute;
	bottom: 20rpx; /* 调整指示点到底部的距离 */
	left: 880rpx !important; /* 默认居中，可以通过transform调整具体位置 */
	// right: 20rpx;
	width: 100%; /* 根据需要调整宽度 */
	display: flex; /* 使用flex布局来排列指示点 */
	// justify-content: center; /* 水平居中 */
}


::v-deep uni-swiper .uni-swiper-dot-active {
  background-color: #fff; /* 选中时的颜色 */
  width: 30rpx;
  height: 11rpx;
  border-radius: 10rpx;
  margin-top: 3rpx;
}

::v-deep .u-notice-bar .u-notice__content__text {
	font-weight: normal !important;
	font-size: 28rpx !important;
}

.focus{
	padding-bottom: 10upx;
	width: 100%;
	background: #fff !important;
	// overflow: hidden;
	
	.focus2{
		width: 95%;
		margin-left: 2%;
		position: relative;
		
		// 首页轮播图
		.screen-swiper {
			width: 100%;
			height: 275rpx;
			// padding: 10rpx 0 0;
			// box-sizing: border-box;
			overflow: hidden;
			border-radius: 20rpx;
			position: relative;
		
			image {
				width: 100%;
				height: 100%;
				border-radius: 20rpx !important;
			}
		}
	}
	background: linear-gradient(to bottom, #c7dffd, #fff);
}
.wap{
	background: #f4f6ff;
	padding-top: 10upx;
	overflow: hidden;
}
.laba{
	display: flex;
	align-items: center;
	background: #FFFFFF;
	width: 95%;
	margin-left: 2.5%;
	height: 35upx;
	border-radius: 20rpx;
	padding: 20upx 0upx;
	margin-top: 10upx 0upx;
	.slef{
		width: 100%;
		display: flex;
		align-items: center;
		padding: 0 20rpx;
		box-sizing: border-box;
	}
	
	image{
		&:first-of-type{
			width: 50rpx;
			height: 50rpx;
			margin-right: 5rpx;
		}
		&:last-of-type{
			width: 50rpx;
			height: 55rpx;
			margin-right: 5rpx;
			transform: scale(.8);
		}
	}
	.acd{
		font-size: 20upx;
		display: inline-block;
		padding-left: 10upx;
		width: 100%;
		transform: translateY(2);
	}
}
.navs{
	background: #f4f6ff;
	width: 100%;
	overflow: hidden;
	padding-top: 5upx;
	// font-family: "Alibaba";
	padding-bottom: 10rpx;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	
	.item{
		// float: left;
		width: 25%;
		text-align: center;
		margin: 20upx 0upx;
		image{
			width: 100rpx;
			height: 100rpx;
		}
		text{
			font-weight: 500;
			display: block;
			color: #333333;
			// font-weight: bold;
			font-size: 24rpx;
			margin-top: -5rpx;
			// font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
			// transform: translateY(18upx);
		}
	}
}
.adv{
	width: 100%;
	margin-top: 20upx;
	// margin-left: 2.5%;
	padding: 0 30rpx;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	justify-content: space-between;
	background-color: #fff;
	
	image{
		width: 49%;
		height: 180rpx;
		border-radius: 20upx;
		&:last-of-type{
			transform: translateX(10upx);
		}
	}
}
.adv3{
	width: 100%;
	margin-top: 30rpx;
	padding: 0 20rpx;
	box-sizing: border-box;
	// margin-left: 2.5%;
	image{
		width: 100%;
		height: 228rpx;
		border-radius: 20rpx;
	}
}

.items {
	width: 100%;
	padding: 0 20rpx;
	box-sizing: border-box;
	margin-top: 20rpx;
	
	.background {
		width: 100%;
		// background-color: #fff;
		border-radius: 20rpx;
		
		.stitle{
			position: relative;
			// height: 90%;
			
			image {
				width: 100%;
				height: 100rpx;
				vertical-align: middle;
			}
			
			.sleft{
				// width: 400rpx;
				// position: relative;
				// display: inline-block;
				font-size: 30rpx;
				position: absolute;
				top: 25rpx;
				left: 30rpx;
				color: #fff;
				font-size: 34rpx;
				font-weight: bold;
				
				font-family: AlimamaShuHeiTiBold;
			}
			.sright{
				display: flex;
				align-items: center;
				position: absolute;
				top: 40rpx;
				right: 20rpx;
				
				color: #869198;
				font-size: 24rpx;
				
				text {
					margin-right: 5rpx;
				}
			}
		}
		
		.box1{
			width: 100%;
			border-radius: 0 0 20rpx 20rpx;
			// padding: 0 0 10rpx;
			box-sizing: border-box;
			box-shadow: 4rpx 4rpx 4rpx 4rpx rgba(0,0,0,0.03);
			// margin-top: -8rpx;
			
			.ul{
				background: linear-gradient(#A3D2FE 0%, #FFFFFF);
				padding: 20rpx 15rpx;
				overflow-x: scroll;
				box-sizing: border-box;
				white-space: nowrap;
				width: 100%;
				overflow:auto;
				
				text{
					display: inline-block;
					white-space: nowrap;
					box-sizing:border-box;
					padding: 8upx 20rpx;
					color: #999999;
					font-size: 24rpx;
					font-weight: 400;
					border: 2rpx solid #fff;
					background-color: #fff;
					border-radius: 10rpx;
					margin-right: 30upx;
					&.act{
						background: rgba(45,154,254,0.12);
						color: #2D9AFE;
						border: 2rpx solid #2D9AFE;
					}
				}
			}
			
		}
		
	}
	
	.backgrounds {
		width: 100%;
		// background-color: #fff;
		border-radius: 20rpx;
		
		.stitle{
			position: relative;
			
			image {
				width: 100%;
				height: 100rpx;
				vertical-align: middle;
			}
			
			.sleft{
				// position: relative;
				// display: inline-block;
				font-size: 30rpx;
				position: absolute;
				top: 25rpx;
				left: 30rpx;
				color: #fff;
				font-size: 34rpx;
				font-family: AlimamaShuHeiTiBold;
				// font-weight: bold;
			}
			.sright{
				display: flex;
				align-items: center;
				position: absolute;
				top: 40rpx;
				right: 20rpx;
				
				color: #869198;
				font-size: 24rpx;
				
				text {
					margin-right: 5rpx;
				}
			}
		}
		
		.box1{
			width: 100%;
			border-radius: 0 0 20rpx 20rpx;
			padding: 0 0 10rpx;
			box-sizing: border-box;
			// background: linear-gradient(#FDE3B2 0%, #FFFFFF 8%);
			box-sizing: border-box;
			box-shadow: 4rpx 4rpx 4rpx 4rpx rgba(0,0,0,0.03);
			
			.ul{
				background: linear-gradient(#FDE3B2 0%, #FFFFFF);
				padding: 20rpx 15rpx;
				overflow-x: scroll;
				// padding: 0upx 20upx;
				box-sizing: border-box;
				white-space: nowrap;
				width: 100%;
				overflow:auto;
				
				text{
					display: inline-block;
					white-space: nowrap;
					box-sizing:border-box;
					padding: 8upx 20rpx;
					color: #999999;
					font-size: 24rpx;
					font-weight: 400;
					border: 2rpx solid #fff;
					background-color: #fff;
					border-radius: 10rpx;
					margin-right: 30upx;
					&.act{
						background: rgba(254,192,76,0.12);
						color: #FEC04C;
						border: 2rpx solid #FEC04C;
					}
				}
			}
			
		}
		
	}
	
	.backgroundss {
		width: 100%;
		// background-color: #fff;
		border-radius: 20rpx;
		
		.stitle{
			position: relative;
			
			image {
				width: 100%;
				height: 100rpx;
				vertical-align: middle;
			}
			
			.sleft{
				// position: relative;
				// display: inline-block;
				font-size: 30rpx;
				position: absolute;
				top: 25rpx;
				left: 30rpx;
				color: #fff;
				font-size: 34rpx;
				font-family: AlimamaShuHeiTiBold;
			}
			.sright{
				display: flex;
				align-items: center;
				position: absolute;
				top: 40rpx;
				right: 20rpx;
				
				color: #869198;
				font-size: 24rpx;
				
				text {
					margin-right: 5rpx;
				}
			}
		}
		
		.box1{
			width: 100%;
			border-radius: 0 0 20rpx 20rpx;
			padding: 0 0 10rpx;
			box-sizing: border-box;
			// background: linear-gradient(#E0F9F9 0%, #FFFFFF 18%);
			box-sizing: border-box;
			box-shadow: 4rpx 4rpx 4rpx 4rpx rgba(0,0,0,0.03);
			
			.ul{
				background: linear-gradient(#E0F9F9 0%, #FFFFFF);
				padding: 20rpx 15rpx;
				overflow-x: scroll;
				// padding: 0upx 20upx;
				box-sizing: border-box;
				white-space: nowrap;
				width: 100%;
				overflow:auto;
				
				text{
					display: inline-block;
					white-space: nowrap;
					box-sizing:border-box;
					padding: 8upx 20rpx;
					color: #999999;
					font-size: 24rpx;
					font-weight: 400;
					border: 2rpx solid #fff;
					background-color: #fff;
					border-radius: 10rpx;
					margin-right: 30upx;
					&.act{
						background: rgba(68,203,204,0.12);
						color: #44CBCC;
						border: 2rpx solid #44CBCC;
					}
				}
			}
			
		}
		
	}
}

.giid{
	display: flex;
	margin-top: 20rpx;
	padding: 0 0 10rpx;
	box-sizing: border-box;
	box-shadow: 0px 4px 4px 0px rgba(0,0,0,0.05);
	border-radius: 0 0 20rpx 20rpx;
	
	.giid_item {
		width: 100%;
		display: flex;
		align-items: center;
		padding: 0 10rpx 20rpx 20rpx;
		// background-color: #fff;
		box-sizing: border-box;
		// margin-top: 10rpx;
	}
	
	image{
		width: 200rpx;
		height: 150rpx;
		border-radius: 20upx;
	}
	.slf{
		// background-color: #fff;
		width: 440rpx;
		border-radius:0upx 20upx 20upx 0upx;
		position: relative;
		padding: 10rpx 5rpx 10rpx 10rpx;
		height: 100%;
		margin-left: 15rpx;
		// padding-left: 4%;
	}
	text{
		display: block;
		&.text1{
			// font-weight: normal !important;
			font-size: 28rpx;
			width: 100%;
			// padding-top: 5rpx;
			color: #222222;
			font-weight: bold;
		}
		&.button{
			position: absolute;
			background: linear-gradient( 360deg, #2D9AFE 0%, #47B8FF 100%);
			color: #fff;
			font-size: 24rpx;
			font-weight: bold;
			border-radius: 30upx;
			padding: 15rpx 25rpx;
			right: 0;
			bottom: 20upx;
		}
	}
}
.k3{
	overflow: hidden;
	padding: 0upx 20rpx;
	box-sizing: border-box;
	.item3{
		box-shadow: 0px 4px 4px 0px rgba(0,0,0,0.05);
		width: 47%;
		float: left;
		border-radius: 20upx;
		margin-bottom: 15rpx;
		padding: 15rpx 0;
		
		&:nth-child(2n){
			float: right;
		}
		.text2{
			margin-top: 10rpx;
			padding: 0 20upx;
			font-weight: bold;
			font-size: 28upx;
			color: #333333;
			line-height: 42upx;
			font-weight: bold;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			overflow:hidden;
		}
		image{
			width: 100%;
			height: 200rpx;
			border-radius: 20upx;
		}
		.firs{
			padding: 10rpx 20upx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.sdd{
				font-size: 26rpx;
				color: #FEC04C;
				font-weight: bold;
				
				text{
					&:last-of-type{
						font-size: 26rpx;
						color: #999;
						margin-left: 10rpx;
					}
				}
			}
		}
		
		.btn {
			display: flex;
			justify-content: center;
			align-items: center;
			
			.bbb{
				width: 200rpx;
				background: linear-gradient( 360deg, #2D9AFE 0%, #47B8FF 100%);
				border-radius: 66upx;
				font-weight: bold;
				font-size: 24rpx;
				color: #FFFFFF;
				padding: 12rpx 26rpx;
				text-align: center;
			}
		}
		
	}
}

.study {
	padding: 0 20rpx 10rpx;
	box-sizing: border-box;
	
	.bf5{
		background-color: #fff;
		box-shadow: 0px 4px 4px 0px rgba(0,0,0,0.05);
		border-radius: 20upx;
		margin-bottom: 20upx;
		margin-top: 10rpx;
		padding: 10rpx 20rpx;
		box-sizing: border-box;
		width: 100%;
		// margin-left: 2%;
		font-family: "Alibaba";
		.text1{
			display: block;
			width: 100%;
			font-weight: bold;
			padding: 8rpx 0upx;
			font-size: 28rpx;
		}
		.sf9{
			display: flex;
			align-items: center;
			justify-content: space-between;
			// font-size: 30upx;
			padding-top: 20rpx;
			
			.s11{
				font-size: 24rpx;
				font-weight: 500;
				color: #222222;
			}
			.sdd{
				color: #626164;
				display: flex;
				align-items: center;
				font-size: 24rpx;
				
				text{
					display: inline-block;
					padding: 0upx 10upx;
			
				}
			}
			
		}
	}
}

.mark{
	width: 100%;
	height: 100vh;
	z-index: 12;
	position: fixed;
	bottom: 0;
	background-color: rgba(#000, .42);
	display: flex;
	align-items: center;
	justify-content: center;
	
	.box5{
		background-image: url(../../static/popup.png);
		background-repeat: no-repeat;
		background-position: center;
		background-size: 100% 100%;
		width: 520rpx;
		margin-left: 2.2%;
		overflow: hidden;
		z-index: 15;
		min-height: 460rpx;
		position: fixed;
		top: 40%;
		transform: translateY(-50%);
		// background-color: #fff;
		border-radius: 20upx;
		.dd5{
			width: 100%;
			height: 60upx;
			position: relative;
		}
		.close{
			width: 60upx;
			height: 60upx;
			border-radius: 30upx;
			background: linear-gradient( 360deg, #2D9AFE 0%, #47B8FF 100%);
			position: absolute;
			right: 0;
			top: 20upx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		image{
			width: 300upx;
			height: 180rpx;
			margin-left: 50%;
			transform: translateX(-50%);
			margin-top: 60rpx;
		}
		.text{
			// font-weight: bold;
			font-size: 32upx;
			color: #000;
			text-align: center;
			font-family: PingFang;
		}
		.but{
			font-weight: bold;
			font-size: 30upx;
			color: #fff;
			text-align: center;
			background-color: #2695ff;
			width: 75%;
			margin-left: 15%;
			margin-top: 50rpx;
			height: 80upx;
			line-height: 80upx;
			border-radius: 50upx;
		}
	}
}