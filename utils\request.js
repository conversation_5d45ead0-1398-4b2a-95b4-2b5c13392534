import {url , uniacid,uploadUrl} from "./api.js";
import lmd5 from './md5.js'
const get = (action , param) => {
	var s = (new Date()).getDate();
	var token = lmd5.hex_md5(encodeURIComponent(lmd5.hex_md5(encodeURIComponent('8975854111ascc')))+s);
	var uniacid = uni.getStorageSync("uniacid");
	let requrl = url+action+"?uniacid="+uniacid+"&timestamp="+Date.now();
	return new Promise ((resolve,reject) => {
		uni.request({
			method:"GET",
			url:requrl,
			header: { 'Authorization': 'Bearer ' + token },
			data:param || {},
			success: (data) => {
				if(data && data.statusCode == 200){
					resolve(data.data);
				}else{
					reject(null);
				}
				
			},
			fail: (error) => {
				reject(error);
			}
		}) 
	}).catch((e) => {});
} 
const post = (action , param) => {
	var s = (new Date()).getDate();
	var token = lmd5.hex_md5(encodeURIComponent(lmd5.hex_md5(encodeURIComponent('8975854111ascc')))+s);
	//console.log("token" , token);
	var uniacid = uni.getStorageSync("uniacid");
	let requrl = url+action+"?uniacid="+uniacid+"&timestamp="+Date.now();
	return new Promise ((resolve,reject) => {
		uni.request({
			method:"POST",
			url:requrl,
			header: { 'Authorization': 'Bearer ' + token },
			data:param || {},
			success: (data) => {
				if(data && data.statusCode == 200){
					resolve(data.data);
				}else{
					reject(null);
				}
			},
			fail: (error) => {
				reject(error);
			}
		})
	}).catch((e) => {});
}
const tourl = (url , redi) => {
	// #ifdef H5
	var uniacid = 0;
	var uid = uni.getStorageSync("uid");
	url = url.indexOf('?') != -1 ? url+"&uid="+uid+"&uniacid="+uniacid : url+"?s=1"+"&uid="+uid+"&uniacid="+uniacid;
	// #endif
	redi = redi || 1;
	if(redi == 1){
		uni.navigateTo({
			url:url
		})
	}else{
		uni.reLaunch({
			url:url
			
		})
	}
}
const upload = (file) => {
	let url = uploadUrl+'index';
	console.log(file);
	return new Promise((resolve , reject) => {
		uni.uploadFile({
			url: url,
			method: 'POST',
			formData: {file: file},
			filePath: file,
			name: 'file',
			header: {},
			success: res => {
				resolve(JSON.parse(res.data).path);
			},
			fail: () => {},
			complete: () => {}
		});
	}).catch((e) => {});
}
const uploadVideo = (file) => {
	let url = uploadUrl+'uploadVideo';
	return new Promise((resolve , reject) => {
		uni.uploadFile({
			url: url,
			method: 'POST',
			formData: {file: file},
			filePath: file,
			name: 'file',
			header: {},
			success: res => {
				console.log(JSON.parse(res.data));
				resolve(JSON.parse(res.data).data.path);
			},
			fail: () => {},
			complete: () => {}
		});
	}).catch((e) => {});
}
const msg = (title, duration=1500, mask=false, icon='none')=>{
	//统一提示方便全局修改
	if(Boolean(title) === false){
		return;
	}
	uni.showToast({
		title,
		duration,
		mask,
		icon
	});
}
export{
	get,
	post,
	url,
	tourl,
	uploadUrl,
	uploadVideo,
	upload,
	msg
}