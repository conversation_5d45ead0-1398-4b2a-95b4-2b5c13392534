<template>
	<view>
		<view class="top">
			<view class="user">
				<image src="../../static/user/<EMAIL>" class="beijing" mode=""></image>
				<image :src="user.headimgurl" class="logo"/>
				<!-- <image src="../../static/user/<EMAIL>" class="logo" mode=""></image> -->
				<view class="slef">
					<view class="sfrit">
						<text class="text1">{{user.nickname}}</text>
						<text class="tag" style="display: none;">正式学员</text>
					</view>
					<view class="sff" @click="$api.tourl('/pages/user/mbind')">
						<image src="/static/user/<EMAIL>"></image>
						<text>{{!user.mobile ? '绑定手机号' : user.mobile}}</text>
					</view>
				</view>
			</view>
			
			<view class="" style="width: 100%; padding: 0 20rpx 10rpx; box-sizing: border-box;">
				<view class="nav">
					<view class="item" v-for="(c,k) in advs3" @click="tourl(c)">
						<image :src="c.tb"></image>
						<text>{{c.mc}}</text>
					</view>
					
				</view>	
			</view>
			
		</view>
		<view class="banner" @click="clickUserAdv">
			<image :src="adv[0].fmtp"></image>
		</view>
		<view class="bnav">
			<view class="item" @click="$api.tourl('/pages/user/editinfo')">
				<image src="/static/user/1.png" class="slg"></image>
				<view>
					<text>修改资料</text>
					<image src="/static/right.png"></image>
				</view>
			</view>
			<view class="item" @click="$api.tourl('/pages/msg/msg')">
				<image src="/static/user/2.png" class="slg"></image>
				<view>
					<text>我的消息</text>
					<image src="/static/right.png"></image>
				</view>
			</view>
			<view class="item"  v-for="(c,k) in advs4" @click="tourl2(c)">
				<image :src="c.tb" class="slg"></image>
				<view>
					<text>{{c.mc}}</text>
					<image src="/static/right.png"></image>
				</view>
			</view>
			<view class="item" @click="$api.tourl('/pages/user/views')">
				<image src="/static/user/3.png" class="slg"></image>
				<view>
					<text>学习记录</text>
					<image src="/static/right.png"></image>
				</view>
			</view>	
			<view class="item" @click="$api.tourl('/pages/user/order')">
				<image src="/static/user/4.png" class="slg"></image>
				<view>
					<text>我的课程</text>
					<image src="/static/right.png"></image>
				</view>
			</view>	
		</view>
		<view class="cop"></view>
		<Footer :act="'user'"></Footer>
	</view>
</template>

<script>
	import Footer from "@/components/footer.vue";
	export default {
		components: {
			Footer
		},
		data() {
			return {
				user:{},
				uid:uni.getStorageSync("uid"),
				advs3:[],
				advs4:[],
				adv:[{"fmtp":""}],
				icon:["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"],
			}
		},
		onShow() {
			this.$api.get('user/getUser',{"uid":this.uid}).then(res => {
				//alert(res.mobile);
				if(!res.mobile || (res.mobile == '')){
					//this.$api.tourl('/pages/user/mbind');
				}
				this.user = res;
				uni.setStorageSync('user' , this.user);
			});
			
		},
		onLoad() {
			this.$api.get('user/getMuint',{'type':'图标菜单'}).then(res => {
				this.advs3 = res;
			})	
			this.$api.get('user/getMuint',{'type':'文字菜单'}).then(res => {
				this.advs4 = res;
			})
			this.$api.get('config/getAdv',{'type':3}).then(res => {
				this.adv = res;
			})
			
			this.$api.get('user/getUserApiUrl',{uid:uni.getStorageSync("uid"),'redirect':3}).then(res => {
				if(res){
					uni.setStorageSync('apikcurl' , res);
				}
			})	
		},
		methods: {
			turl2(){
				if (!this.user.mobile) {
					uni.showModal({
						title: '提示：',
						content: '请先完善个人信息，然后点确定再跳转到修改信息页面！',
						success: (res) => {
							if (res.confirm) {
								this.$api.tourl('/pages/user/editinfo');
							} else if (res.cancel) {
								
							}
						}
					});
				}else{
					this.$api.get('user/getUserApiUrl',{uid:this.uid,'redirect':1}).then(res => {
						location.href = res;
					})	
				}
				
			},
			turl3(){
				if (!this.user.mobile) {
					uni.showModal({
						title: '提示：',
						content: '请先完善个人信息，然后点确定再跳转到修改信息页面！',
						success: (res) => {
							if (res.confirm) {
								this.$api.tourl('/pages/user/editinfo');
							} else if (res.cancel) {
								
							}
						}
					});
				}else{
					this.$api.get('user/getUserApiUrl',{uid:this.uid,'redirect':2}).then(res => {
						location.href = res;
					})
				}
				
			},
			clickUserAdv(){
				if(this.adv[0].ljdz == ''){
					this.$api.tourl('/pages/adv/adv?id='+this.adv[0].id);
				}else{
					location.href = this.adv[0].ljdz;
				}
			},
			tourl2(c){
				if(c.ljdz.indexOf('?') != -1){
					location.href = c.ljdz+'&openid='+this.user.openid;
				}else{
					location.href = c.ljdz+'?openid='+this.user.openid;
				}
				
			},
			tourl(c){
				if(c.ljdz == ''){
					this.$api.tourl('/pages/adv/adv?id='+c.id);
				}else{
					if(c.ljdz.indexOf('?') != -1){
						location.href = c.ljdz+'&openid='+this.user.openid;
					}else{
						location.href = c.ljdz+'?openid='+this.user.openid;
					}
				}
			}
		}
	}
</script>

<style src="./user.scss" lang="scss"></style>
