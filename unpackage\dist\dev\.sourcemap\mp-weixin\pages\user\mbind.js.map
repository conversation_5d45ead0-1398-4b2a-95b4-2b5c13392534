{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/kecheng_v3/pages/user/mbind.vue?5063", "webpack:///D:/work/kecheng_v3/pages/user/mbind.vue?4454", "webpack:///D:/work/kecheng_v3/pages/user/mbind.vue?925a", "webpack:///D:/work/kecheng_v3/pages/user/mbind.vue?16c5", "uni-app:///pages/user/mbind.vue", "webpack:///D:/work/kecheng_v3/pages/user/mbind.vue?ea2b", "webpack:///D:/work/kecheng_v3/pages/user/mbind.vue?cfe3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "uid", "user", "onShow", "onLoad", "methods", "isPoneAvailable", "subt"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoBtnB;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;MACAF;IACA;MACA;IACA;EACA;EACAG,2BAEA;EACAC;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;MACA;MACA;QACA;QACA;MACA;MACA;QACAP;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAA6oC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAjqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/mbind.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/mbind.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mbind.vue?vue&type=template&id=7f85e91f&\"\nvar renderjs\nimport script from \"./mbind.vue?vue&type=script&lang=js&\"\nexport * from \"./mbind.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mbind.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/mbind.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mbind.vue?vue&type=template&id=7f85e91f&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mbind.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mbind.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"box\">\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t<text>手机号</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"input\">\r\n\t\t\t\t\t<input placeholder=\"请输入手机号\" v-model=\"user.mobile\" placeholder-class=\"pla\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\t\r\n\t\t<view class=\"cop\"></view>\r\n\t\t<view class=\"subt\" @click=\"subt\">\r\n\t\t\t<text>提交</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuid: uni.getStorageSync('uid') || 0,\r\n\t\t\t\tuser: {},\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.$api.get('user/getUser', {\r\n\t\t\t\tuid: this.uid\r\n\t\t\t}).then(res => {\r\n\t\t\t\tthis.user = res;\r\n\t\t\t})\r\n\t\t},\r\n\t\tonLoad() {\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tisPoneAvailable(t) {\r\n\t\t\t\treturn !!/^[1][1,3,2,4,5,7,8,9][0-9]{9}$/.test(t);\r\n\t\t\t},\r\n\t\t\tsubt() {\r\n\t\t\t\tif (this.user.mobile == \"\") {\r\n\t\t\t\t\tthis.$api.msg(\"请输入手机号\");\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.isPoneAvailable(this.user.mobile)) {\r\n\t\t\t\t\tthis.$api.msg(\"请输入有效的手机号!\");\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tthis.$api.post('user/updateUser', {\r\n\t\t\t\t\tdata: this.user\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthis.$api.msg(\"保存成功\");\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.u-radio-group{\r\n\t\tposition: relative;\r\n\t\ttransform: translateX(20upx);\r\n\t\t.u-radio{\r\n\t\t\t&:last-of-type{\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: 120upx;\t\t\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.box {\r\n\t\tbackground-color: #fff;\r\n\t\twidth: 94%;\r\n\t\tmargin-left: 3%;\r\n\t\tmargin-top: 20rpx;\r\n\t\tborder-radius: 20upx;\r\n\t\tfont-family: PingFang SC, PingFang SC;\r\n\r\n\t\t.item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tpadding: 30upx;\r\n\t\t\tmargin-bottom: 30upx;\r\n\t\t\tborder-bottom: 1px solid #F0F0F0;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t.left {\r\n\t\t\t\twidth: 240upx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tfont-size: 32upx;\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t}\r\n\r\n\t\t\t.input {\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 32upx;\r\n\t\t\t\tcolor: #333333;\r\n\r\n\t\t\t\tinput {\r\n\t\t\t\t\tfont-size: 32upx;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\tborder: 2rpx solid #333;\r\n\t\t\t\t\tpadding: 10rpx 20rpx;\r\n\t\t\t\t\tborder-radius: 80rpx;\r\n\t\t\t\t\t// box-sizing: bor;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.pla {\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tfont-size: 32upx;\r\n\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tradio {\r\n\t\t\t\t\ttransform: scale(.72);\r\n\t\t\t\t\tpadding-left: 20upx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&:last-of-type {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tpage {\r\n\t\tfont-family: PingFang SC, PingFang SC;\r\n\t\tbackground-color: #F7F8FA;\r\n\t}\r\n\r\n\t.subt {\r\n\t\tbackground: #2695FF;\r\n\t\tborder-radius: 47px 47px 47px 47px;\r\n\t\ttext-align: center;\r\n\t\twidth: 94%;\r\n\t\tmargin-left: 3%;\r\n\t\theight: 90upx;\r\n\t\tline-height: 90upx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 32upx;\r\n\t\tposition: fixed;\r\n\t\tz-index: 1;\r\n\t\tbottom: 20rpx;\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mbind.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mbind.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1735872263967\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}