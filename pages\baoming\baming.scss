page {
	font-family: 'PingFang'; 
	background-image: url('@/static/bg3.png');
	background-size: 100% 100%;
	min-height: 100vh;
	width: 100%;
}

.navs{
	margin-top: 20rpx;
	width: 100%;
	margin-left: 20rpx;
	overflow-x: scroll;
	white-space: nowrap;
	padding: 20rpx 30rpx 0rpx 30rpx;
	text{
		display: inline-block;
		white-space: nowrap;
		box-sizing:border-box;
		padding: 8rpx 20rpx;
		background-color: #FFFFFF;
		font-weight: 500;
		font-size: 28upx;
		color: #555555;
		border-radius: 30rpx;
		margin-right: 30upx;
		&.act{
			background: #2D9AFE;
			border-radius: 20px 20px 20px 20px;
			
			color: #FFFFFF;
		}
	}
}
.giid{
	margin-top: 40upx;
	width: 100%;
	padding: 0 40rpx;
	box-sizing: border-box;
	
	.item {
		display: flex;
		padding: 20rpx;
		box-sizing: border-box;
		background-color: #fff;
		margin-bottom: 20rpx;
		border-radius: 20rpx;
		box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(0,0,0,0.05);
	}
	// margin-left: 3%;
	image{
		width: 200rpx;
		height: 150rpx;
		border-radius: 20upx;
	}
	.slf{
		// background-color: #fff;
		width: 480rpx;
		border-radius:0upx 20upx 20upx 0upx;
		position: relative;
		// padding-left: 4%;
		padding: 5rpx 10rpx 10rpx 20rpx;
		box-sizing: border-box;
	}
	text{
		display: block;
		&.text1{
			font-family: PingFang SC, PingFang SC;
			font-weight: normal !important;
			font-size: 28rpx;
			width: 100%;
			// padding-top: 5rpx;
			color: #222222;
		}
		&.button{
			position: absolute;
			background: linear-gradient( 360deg, #2D9AFE 0%, #47B8FF 100%);
			color: #fff;
			font-size: 24rpx;
			font-weight: bold;
			border-radius: 30upx;
			padding: 12rpx 25rpx;
			right: 0;
			bottom: 0;
		}
	}
}