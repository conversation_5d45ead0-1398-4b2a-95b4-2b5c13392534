<template>
	<view>
		<view class="top">
			<video :src="row.video.play_url"></video>
		</view>
		<view class="recordinfo">
			<view>{{row.video.title}}</view>
			<view>{{row.video.subject_title	}} {{row.video.chapter_title}}</view>
		</view>
		<block v-for="(c,k) in row.lclist">
			<block v-for="(c2,k2) in c.children">
				<block v-for="(c3,k3) in c2.chapter">
					<view class="chapter_title">
						<text>{{c3.title}}</text>
					</view>
					<view class="recordcontent">
						<view class="recordlist">
							<block v-for="(c4,k4) in c3.video_list">
							<view :class="c4.id == video_id ? 'active' : ''" v-if="!row.isbuy" style="position: relative;">
								<image v-if="c4.id == video_id" style="width: 15px;height: 15px;" src="https://njt.dxwmedu.com/static/template/mobile/img/play.png"></image>
								<image v-if="c4.id != video_id" style="width: 15px;height: 15px;" src="https://njt.dxwmedu.com/static/template/mobile/img/lock.png"></image>
								<text>{{c4.title}}</text>
								<image style="display: none;" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOBAMAAADtZjDiAAAAJ1BMVEXp6uxHcEzn6ezm6+vo6e3m5uno6uzo6e3n6Ovo6uzp6ezn5+vo6eyeNRA0AAAADHRSTlP1AFcZ0wmGPXqysEHTAOJfAAAAUElEQVQI12MQFE5hcDMUZBBUOHPmDJMgg9CZ2aE7zygyGJwQFBTsYWbIWQikpY4x+BQCafEjDDyBQFr0AJyGicPUwfQJnZkeWgk0B2Yu1B4AMEocKbdHGBkAAAAASUVORK5CYII="></image>
							</view>
							<view v-if="row.isbuy" @click="kan(c4.id)"  :class="c4.id == video_id ? 'active' : ''" style="position: relative;">
								<image  style="width: 15px;height: 15px;" src="https://njt.dxwmedu.com/static/template/mobile/img/play.png"></image>
								
								<text>{{c4.title}}</text>
								<image style="display: none;" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOBAMAAADtZjDiAAAAJ1BMVEXp6uxHcEzn6ezm6+vo6e3m5uno6uzo6e3n6Ovo6uzp6ezn5+vo6eyeNRA0AAAADHRSTlP1AFcZ0wmGPXqysEHTAOJfAAAAUElEQVQI12MQFE5hcDMUZBBUOHPmDJMgg9CZ2aE7zygyGJwQFBTsYWbIWQikpY4x+BQCafEjDDyBQFr0AJyGicPUwfQJnZkeWgk0B2Yu1B4AMEocKbdHGBkAAAAASUVORK5CYII="></image>
							</view>
							</block>
						</view>
					</view>
				</block>	
			</block>	
		</block>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id:0,
				on:0,
				video_id:0,
				row:{"video":{"play_url":""}}
			}
		},
		onLoad(e) {
			this.id = e.id || 0;
			this.video_id = e.video_id  || 0,
			this.$api.get('kecheng/row',{"video_id":this.video_id,id:this.id,'uid':uni.getStorageSync('uid')}).then(res => {
				this.row = res;
			})
		},
		methods: {
			kan(video_id){
				this.video_id = video_id;
				this.$api.get('kecheng/row',{"video_id":video_id,id:this.id,'uid':uni.getStorageSync('uid')}).then(res => {
					this.row = res;
					uni.pageScrollTo({
						scrollTop: 0,
						duration: 100
					})
				})
			},
			getY(k){
				var a = ['一','二','三','四','五','六','七','八','九','十','十一','十二','十三','十四','十五','十六','十七','十八','十九','二十'];
				return a[k];
			},
		}
	}
</script>

<style lang="scss" src="./video.scss"></style>
