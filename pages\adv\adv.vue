<template>
	<view>
		<view class="top">
			<view class="title">
				{{row.bt}}
			</view>
			<view class="sdd">
				<u-icon name="clock" color="#fff" size="14"></u-icon>
				<text>{{row.optdt}}</text>
			</view>
			<view class="box">
				<view class="d">
					<jyf-parser :html="row.nr" ref="article"></jyf-parser>　
				</view>
			</view>
			<view class="adv">
				<image :src="adv[0].fmtp" @click="tourl()"></image>
			</view>
		</view>
		<Files :files="row.files"></Files>
		<view class="cop"></view>
	</view>
</template>

<script>
	import jyfParser from "@/components/jyf-parser/jyf-parser";
	import Files from "@/components/files.vue";
	export default {
		components: {
			jyfParser,
			Files
		},
		data() {
			return {
				id: 0,
				adv: [{
					"ljdz": ""
				}],
				row: {},
				tab: "",
			}
		},
		onLoad(e) {
			this.$api.msg("数据读取中...");
			this.id = e.id || 0;
			this.$api.get('config/advRow', {
				id: this.id,
			}).then(res => {
				this.row = res;
			})
			this.$api.get('config/getAdv', {
				'type': 5
			}).then(res => {
				this.adv = res;
			})
		},
		methods: {
			tourl() {
				if (this.adv[0].ljdz == '') {
					this.$api.tourl('/pages/adv/adv?id=' + this.adv[0].id);
				} else {
					location.href = this.adv[0].ljdz;
				}

			}
		}
	}
</script>

<style lang="scss">
	page {
		font-family: 'PingFang';
		background: #F4F6FA;
	}

	.adv {
		position: fixed;
		z-index: 3;
		right: 0;
		bottom: 100upx;
		height: 300upx;

		// width: 300upx;
		image {
			width: 200rpx;
			height: 100rpx;
		}
	}

	.top {
		width: 100%;
		height: 500upx;
		background: linear-gradient( 180deg, #2695FF 0%, rgba(38,149,255,0) 100%);
		border-radius: 0px 0px 0px 0px;

		.title {
			padding: 30upx 40upx 20upx 40upx;
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 34upx;
			color: #fff;
		}

		.sdd {
			display: flex;
			align-items: center;
			padding-left: 40upx;

			image {
				width: 60upx;
				height: 60upx;
			}

			text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 30upx;
				padding-left: 10upx;
				color: #fff;
			}
		}

		
	}
	.box {
		width: 96%;
		margin-left: 2%;
		background-color: #fff;
		border-radius: 16upx;
		height: 50vh;
		margin-top: 30upx;
	
		.d {
			padding: 30upx;
			font-size: 30upx;
		}
	}
</style>