{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/kecheng_v3/pages/baoming/baoming.vue?b748", "webpack:///D:/work/kecheng_v3/pages/baoming/baoming.vue?912c", "webpack:///D:/work/kecheng_v3/pages/baoming/baoming.vue?9ecd", "webpack:///D:/work/kecheng_v3/pages/baoming/baoming.vue?b0f0", "uni-app:///pages/baoming/baoming.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Footer", "data", "blx", "baocats", "nodata", "page", "baolist", "onLoad", "onReachBottom", "methods", "tonbap", "location", "clickCat", "setbCat"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAomB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCqBxnB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;MACA;MACA;MACA;IACA;EACA;EACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACAC;IACAC;MACAC;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/baoming/baoming.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/baoming/baoming.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./baoming.vue?vue&type=template&id=68b8e85c&\"\nvar renderjs\nimport script from \"./baoming.vue?vue&type=script&lang=js&\"\nexport * from \"./baoming.vue?vue&type=script&lang=js&\"\nimport style0 from \"./baming.scss?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/baoming/baoming.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./baoming.vue?vue&type=template&id=68b8e85c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./baoming.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./baoming.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\r\n\t\t<view class=\"navs\">\r\n\t\t\t<text :class=\"'' == blx ? 'act' : ''\" @click=\"clickCat('')\">{{'全部分类'}}</text>\r\n\t\t\t\t\r\n\t\t\t<text v-for=\"(c,k) in baocats\" :class=\"c.lx == blx ? 'act' : ''\" @click=\"clickCat(c.lx)\">{{c.lx}}</text>\r\n\t\t</view>\r\n\t\t<view class=\"giid\" v-for=\"(c,k) in baolist\">\r\n\t\t\t<image :src=\"c.fmtp\" mode=\"\" @click=\"tonbap(c.bmdz)\"></image>\r\n\t\t\t<view class=\"slf\">\r\n\t\t\t\t<text class=\"text1\" @click=\"tonbap(c.bmdz)\">{{c.bt}}</text>\r\n\t\t\t\t<text class=\"button\" @click=\"tonbap(c.bmdz)\">立刻报名</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"cop\">{{nodata}}</view>\r\n\t\t<view class=\"cop\"></view>\n\t</view>\n</template>\n\n<script>\n\timport Footer from \"@/components/footer.vue\";\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tFooter\r\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tblx:\"\",\r\n\t\t\t\tbaocats:[],\r\n\t\t\t\tnodata:\"\",\r\n\t\t\t\tpage:1,\r\n\t\t\t\tbaolist:[],\n\t\t\t}\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.$api.get('baoming/cats').then(res => {\r\n\t\t\t\tthis.baocats = res;\r\n\t\t\t})\r\n\t\t\tthis.setbCat('');\r\n\t\t},\r\n\t\tonReachBottom(){\r\n\t\t\tif(this.nodata == \"\"){\r\n\t\t\t\tthis.page += 1;\r\n\t\t\t\tthis.$api.msg(\"数据读取中...\");\r\n\t\t\t\tthis.setbCat(this.blx);\r\n\t\t\t}\r\n\t\t},\r\n\t\t/*\r\n\t\t//上拉\r\n\t\tonReachBottom(){\r\n\t\t\tthis.page = this.page > 1 ? this.page - 1 : 1;\r\n\t\t\tthis.setbCat(this.blx);\r\n\t\t},\r\n\t\t//下拉\r\n\t\tonPullDownRefresh(){\r\n\t\t\tthis.page += 1; \r\n\t\t\tthis.setbCat(this.blx);\r\n\t\t},\r\n\t\t*/\n\t\tmethods: {\n\t\t\ttonbap(url){\r\n\t\t\t\tlocation.href = url;\r\n\t\t\t},\r\n\t\t\tclickCat(lx){\r\n\t\t\t\tthis.blx = lx;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.baolist = [];\r\n\t\t\t\tthis.setbCat(lx);\r\n\t\t\t},\r\n\t\t\tsetbCat(lx){\r\n\t\t\t\tthis.blx = lx;\r\n\t\t\t\tthis.$api.get('baoming/getList', {\r\n\t\t\t\t\t'lx':lx,\r\n\t\t\t\t\t\"page\":this.page,\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\t//uni.stopPullDownRefresh();\r\n\t\t\t\t\t//this.baolist = res;\r\n\t\t\t\t\tif(!res || (res.length == 0)){\r\n\t\t\t\t\t\tthis.nodata = \"暂无更多数据\";\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.baolist = this.page == 1 ? res : this.baolist.concat(res);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style src=\"./baming.scss\" lang=\"scss\"></style>\n"], "sourceRoot": ""}