<view class="content"><view class="top"><view class="left"><image src="/static/logo.png"></image></view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="right" bindtap="__e"><view class="inpuit"><image src="/static/search.png"></image><text>国家开放大学</text></view></view></view><view class="sdd3"></view><view class="focus"><view class="focus2"><swiper class="screen-swiper" dots-class="custom-dots" autoplay="{{true}}" interval="2000" duration="500" indicator-dots="true" indicator-color="rgba(255,255,255,0.7)" indicator-active-color="#fff"><block wx:for="{{focus}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item><image src="{{item.fmtp}}" mode="aspectFill"></image></swiper-item></block></swiper></view></view><view class="wap"><view class="laba"><view data-event-opts="{{[['tap',[['toadv',['$0'],['text1.__$n0']]]]]}}" class="slef" bindtap="__e"><image src="/static/laba.png"></image><view class="acd"><u-notice-bar vue-id="8dd740cc-1" text="{{text1[0].bt}}" fontSize="{{12}}" speed="{{15}}" bind:__l="__l"></u-notice-bar></view></view></view></view><view class="navs"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="item" bindtap="__e"><image src="/static/s1.png"></image><text>学历教育</text></view><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="item" bindtap="__e"><image src="/static/s2.png"></image><text>职称评审</text></view><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="item" bindtap="__e"><image src="/static/s3.png"></image><text>职业资格</text></view><view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" class="item" bindtap="__e"><image src="/static/s4.png"></image><text>资格证书</text></view><view data-event-opts="{{[['tap',[['e5',['$event']]]]]}}" class="item" bindtap="__e"><image src="/static/s5.png"></image><text>企业培训</text></view><block wx:for="{{navs}}" wx:for-item="c" wx:for-index="k"><view data-event-opts="{{[['tap',[['toadv',['$0'],[[['navs','',k]]]]]]]}}" class="item" bindtap="__e"><image src="{{c.tb}}"></image><text>{{c.mc}}</text></view></block><view data-event-opts="{{[['tap',[['e6',['$event']]]]]}}" class="item" bindtap="__e"><image src="/static/s6.png"></image><text>全部</text></view></view><view class="adv"><block wx:for="{{advs1}}" wx:for-item="c" wx:for-index="k"><image src="{{c.fmtp}}" data-event-opts="{{[['tap',[['toadv',['$0'],[[['advs1','',k]]]]]]]}}" bindtap="__e"></image></block></view><view class="items"><view class="background"><view class="stitle"><image src="../../static/background.png" mode></image><text data-event-opts="{{[['tap',[['e7',['$event']]]]]}}" class="sleft s1" bindtap="__e">在线报名</text><view data-event-opts="{{[['tap',[['e8',['$event']]]]]}}" class="sright" bindtap="__e"><text>查看全部</text><u-icon vue-id="8dd740cc-2" name="arrow-right" color="#869198" size="14" bind:__l="__l"></u-icon></view></view><view class="box1"><view class="ul"><text data-event-opts="{{[['tap',[['setbCat',['']]]]]}}" class="{{[''==blx?'act':'']}}" bindtap="__e">全部</text><block wx:for="{{baocats}}" wx:for-item="c" wx:for-index="k"><text data-event-opts="{{[['tap',[['setbCat',['$0'],[[['baocats','',k,'lx']]]]]]]}}" class="{{[c.lx==blx?'act':'']}}" bindtap="__e">{{c.lx}}</text></block></view><block wx:for="{{baolist}}" wx:for-item="c" wx:for-index="k"><view class="giid"><view class="giid_item"><image src="{{c.fmtp}}" mode data-event-opts="{{[['tap',[['tonbap',['$0'],[[['baolist','',k,'bmdz']]]]]]]}}" bindtap="__e"></image><view class="slf"><text data-event-opts="{{[['tap',[['tonbap',['$0'],[[['baolist','',k,'bmdz']]]]]]]}}" class="text1" bindtap="__e">{{c.bt}}</text><text data-event-opts="{{[['tap',[['tonbap',['$0'],[[['baolist','',k,'bmdz']]]]]]]}}" class="button" bindtap="__e">立刻报名</text></view></view></view></block></view></view></view><view class="adv3"><block wx:for="{{advs2}}" wx:for-item="c" wx:for-index="k"><image src="{{c.fmtp}}" mode data-event-opts="{{[['tap',[['toadv',['$0'],[[['advs2','',k]]]]]]]}}" bindtap="__e"></image></block></view><view class="items"><view class="backgrounds"><view class="stitle"><image src="../../static/background1.png" mode></image><text data-event-opts="{{[['tap',[['e9',['$event']]]]]}}" class="sleft s1" bindtap="__e">体验课</text><view data-event-opts="{{[['tap',[['e10',['$event']]]]]}}" class="sright" bindtap="__e"><text>查看全部</text><u-icon vue-id="8dd740cc-3" name="arrow-right" color="#869198" size="14" bind:__l="__l"></u-icon></view></view><view class="box1"><view class="ul"><text data-event-opts="{{[['tap',[['sekbCat',['']]]]]}}" class="{{[''==klx?'act':'']}}" bindtap="__e">全部</text><block wx:for="{{kcat}}" wx:for-item="c" wx:for-index="k"><text data-event-opts="{{[['tap',[['sekbCat',['$0'],[[['kcat','',k,'lx']]]]]]]}}" class="{{[klx==c.lx?'act':'']}}" bindtap="__e">{{c.lx}}</text></block></view><view class="k3"><block wx:for="{{kecheng}}" wx:for-item="c" wx:for-index="k"><view data-event-opts="{{[['tap',[['e11',['$event']]]]]}}" data-event-params="{{({c})}}" class="item3" bindtap="__e"><image src="{{c.fmtp}}" mode="aspectFill"></image><view class="text2">{{''+c.bt+''}}</view><view class="firs"><view class="sdd"><text>{{c.ygm}}</text><text>人已购买</text></view></view><view class="btn"><view class="bbb">免费试听</view></view></view></block></view></view></view></view><view class="items"><view class="backgroundss"><view class="stitle"><image src="../../static/background2.png" mode></image><text data-event-opts="{{[['tap',[['e12',['$event']]]]]}}" class="sleft s1" bindtap="__e">学习资讯</text><view data-event-opts="{{[['tap',[['e13',['$event']]]]]}}" class="sright" bindtap="__e"><text>查看全部</text><u-icon vue-id="8dd740cc-4" name="arrow-right" color="#869198" size="14" bind:__l="__l"></u-icon></view></view><view class="box1"><view class="ul"><text data-event-opts="{{[['tap',[['setNewscat',['']]]]]}}" class="{{[''==tab?'act':'']}}" bindtap="__e">全部</text><block wx:for="{{newscat}}" wx:for-item="c" wx:for-index="k"><text data-event-opts="{{[['tap',[['setNewscat',['$0'],[[['newscat','',k,'lx']]]]]]]}}" class="{{[c.lx==tab?'act':'']}}" bindtap="__e">{{c.lx}}</text></block></view><view class="study"><block wx:for="{{newslist}}" wx:for-item="c" wx:for-index="k"><view data-event-opts="{{[['tap',[['tourlIxun',['$0'],[[['newslist','',k]]]]]]]}}" class="bf5" bindtap="__e"><text class="st text1">{{c.bt}}</text><view class="sf9"><text class="s11">{{c.lx}}</text><view class="sdd"><u-icon vue-id="{{'8dd740cc-5-'+k}}" name="eye" color="#626164" size="18" bind:__l="__l"></u-icon><text>{{c.lls}}</text><u-icon vue-id="{{'8dd740cc-6-'+k}}" name="clock" color="#626164" size="18" bind:__l="__l"></u-icon><text>{{c.optdt}}</text></view></view></view></block></view></view></view></view><block wx:if="{{showmsg}}"><view class="mark"><view class="box5"><view class="dd5"><view data-event-opts="{{[['tap',[['setshowmsg']]]]}}" class="close" bindtap="__e"><u-icon vue-id="8dd740cc-7" name="close" color="#fff" size="17" bind:__l="__l"></u-icon></view><image src></image><view class="text">你有新的未读消息...</view><view data-event-opts="{{[['tap',[['e14',['$event']]]]]}}" class="but" bindtap="__e">点击查看</view></view></view></view></block><view class="cop"></view><footer vue-id="8dd740cc-8" act="index" bind:__l="__l"></footer></view>