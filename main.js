import App from './App'
import {get,post,uploadUrl,upload,tourl,msg,uploadVideo} from "@/utils/request.js";
// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
import uView from 'uview-ui';
Vue.use(uView);
Vue.prototype.$api = {msg,get,post,uploadUrl,upload,msg,tourl,uploadVideo};
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif