<template>
	<view>
		<view class="navs">
			<text v-for="(c,k) in cats" @click="setc2(c.name,c.tab)" :class="c.name == cname ? 'act' : ''">{{c.name}}</text>
		</view>
		
		<view class="box" v-for="(c,k) in list" @click="tourlIxun(c)">
			<text class="text1">{{c.bt}}</text>
			<view class="sf">
				<text class="bt">{{c.lx}}</text>
				<view class="sft">
					<view class="sdd">
						<image src="/static/14.png"></image>
						<text>{{c.views}}</text>
					</view>
					<view class="sdd">
						<image src="/static/15.png"></image>
						<text>{{c.optdt}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!--弹窗-->
		<view class="mack" v-if="mark" @click="setHide()"></view>
		<view class="shimw" v-if="mark">
			<!-- <view class="stt">
				<text>{{cname}}</text>
				<u-icon name="arrow-right" color="#fff" size="12"></u-icon>
			</view> -->
			<view class="xxx">
				<view class="left">
					<view class="items">
						<text v-for="(c,k) in cats" @click="setC(c,k)" :class="c.name == cname ? 'act' : ''">{{c.name}}</text>
					</view>
					<view class="fb" @click="chongzhi()">重置</view>
				</view>
				<view class="sright">
					<view class="box1">
						<view class="item" v-for="(c,k) in cats" v-if="cname == '全部分类' ">
							<view class="stt2" style="font-size: 12px;">{{c.name}}</view>
							<view class="ul">
								<text v-for="(c2,k2) in c.child" :class="lx == c2.lx ? 'on' : ''" @click="setchild(c2.lx,c.tab)">{{c2.lx}}</text>
								
							</view>
						</view>
						<view class="item"  v-if="cname != '全部分类' ">
							<view class="stt2" style="font-size: 12px;" v-if>{{cname}}</view>
							<view class="ul">
								<text v-for="(c2,k2) in cats[ck].child" :class="lx == c2.lx ? 'on' : ''" @click="setchild(c2.lx,cats[ck].tab)">{{c2.lx}}</text>
								
							</view>
						</view>
					</view>
					<view class="butt" @click="butt()"><text>确定</text></view>
				</view>
			</view>
		</view>
		<!--弹窗-->
		
		<view class="cop">{{nodata}}</view>
		<view class="cop"></view>
		<Footer :act="''"></Footer>
	</view>
</template>

<script>
	import Footer from "@/components/footer.vue";
	export default {
		components: {
			Footer
		},
		data() {
			return {
				mark:false,
				cname:"全部分类",
				cats:[],
				list:[],
				ck: 0,
				nodata:"",
				lx:"",
				page:1,
				tab:"",
			}
		},
		onLoad(e) {
			
			this.cname = "全部分类";
			this.tab = e.tab || "";
			this.$api.msg("数据读取中...");
			this.$api.get('zixun/getCats').then(res => {
				this.cats = res;
			})
			if(this.tab == ''){
				this.mark = true;
			}else if(this.tab != 'all'){
				if(this.tab == 'xljy'){
					this.cname = "学历教育";
				}else if(this.tab == 'zcps'){
					this.cname = "职称评审";
				}else if(this.tab == 'zyzg'){
					this.cname = "职业资格";
				}else if(this.tab == 'zgzs'){
					this.cname = "资格证书";
				}else if(this.tab == 'qypx'){
					this.cname = "企业培训";
				}
			}else{
				this.setHide();
			}
			this.getData();
		},
		onReachBottom(){
			if(this.nodata == ""){
				this.page += 1;
				this.$api.msg("数据读取中...");
				this.getData();
			}
		},
		methods: {
			tourlIxun(res){
				//'/pages/news/show?id='+c.id
				if(res && (res.ljdz && (res.ljdz != ''))){
					location.href = res.ljdz;
					return false;
				}else{
					this.$api.tourl('/pages/show/show?id='+res.id+'&tab='+res.s1)
				}
			},
			getData(){
				this.$api.get('zixun/getList' , {"tab":this.tab,"page":this.page,"lx":this.lx}).then(res => {
					//this.list = res;
					if(!res || (res.length == 0)){
						this.nodata = "暂无更多数据";
					}else{
						this.list = this.page == 1 ? res : this.list.concat(res);
					}
				})
			},
			setc2(sc,tab){
				this.cname = sc;
				this.tab = tab;
				this.page = 1;
				this.nodata = "";
				if(sc == '全部分类'){
					this.mark = true;
				}else{
					this.$api.msg("数据读取中...");
					this.getData();
				}
			},
			chongzhi(){
				this.page = 1;
				this.nodata = "";
				this.$api.msg("数据读取中...");
				this.lx = "";
				this.tab = "";
				this.getData();
				this.setHide();
			},
			setC(cat,k){
				this.cname = cat.name;
				this.ck = k;
				this.lx = "";
				this.tab = "";
				//console.log(this.cats[k]);
			},
			setHide(){
				this.mark = !this.mark;
			},
			butt(){
				if(this.lx ==  ""){
					this.page = 1;
					this.$api.msg("数据读取中...");
					this.tab = this.cats[this.ck].tab;
					this.getData();
				}
				this.setHide();
			},
			setchild(lx , tab){
				this.lx = lx;
				this.tab = tab;
				this.$api.msg("数据读取中...");
				this.page = 1;
				this.list = [];
				this.nodata = "";
				this.getData();
				//this.setHide();
			}
		}
	}
</script>

<style lang="scss" src="./ziliao.scss"></style>
