<template>
	<view>
	    <view class="nav-tabs">
			<view class="tab-item" v-for="(c,k) in navs" :key="k" :class="{'active': activeTab === k}" @click="switchTab(k)">
				<text>{{c.lx}}</text>
			</view>
		</view>
		
		<view class="training-list">
			<view class="training-item" v-for="(c,k) in list" :key="k" @click="tourlIxun(c)">
				<image class="training-image" :src="c.fmtp || '/static/default-building.jpg'"></image>
				<view class="training-content">
					<view class="training-title text-ellipsis">{{ c.bt }}</view>
					<view class="training-info">
						<view class="training-tag">{{c.lx}}</view>
						<view class="training-stats">
							<view class="stat-item">
								<image src="/static/14.png" style="width: 38upx;height: 38upx;"></image>
								<text>{{c.views || ''}}</text>
							</view>
							<view class="stat-item">
								<image src="/static/15.png"></image>
								<text style="padding-left: 10upx;">{{c.optdt || ''}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<view class="cop">{{nodata || '暂无数据'}}</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				navs:[],
				activeTab: 0,
				nodata:"",
				list:[],
			}
		},
		onLoad(){
			this.getNavs();
			
		},
		methods: {
			switchTab(index) {
				this.activeTab = index;
				this.getList();
			},
			getNavs(){
				this.$api.get('kecheng/getjingpinClass').then(res=>{
					this.navs = res;
					this.getList();
				});
			},
			getList(){
				this.$api.get('kecheng/getjingpinList',{lx:this.navs[this.activeTab].lx}).then(res=>{
					this.list = res;
					this.nodata = res.length > 0 ? '' : '暂无数据';
				});
			},
			tourlIxun(c){
				if (c.ljdz == '') {
					this.$api.tourl('/pages/topcource/show?id=' + c.id);
				} else {
					location.href = c.ljdz;
				}
			}
		}
	}
</script>

<style lang="scss">
page {
	background-image: url("@/static/bg3.png");
	background-size: 100% 100%;
	min-height: 100vh;
	width: 100%;
}

.nav-tabs {
	display: flex;
	padding: 30upx;
	border-radius: 0 0 20upx 20upx;

	
	.tab-item {
		margin-right: 50upx;
		border-radius: 50upx;
		background-color: #f2f9ff;
		padding: 10upx 30upx;
		
		text {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 14px;
			color: #333333;
		}
		
		&.active {
			background-color: #2d9afe;
			
			text {
				color: #fff;
				font-weight: 500;
			}
		}
	}
}


.training-list {
    padding: 0upx 30upx;
}

.training-item {
    display: flex;
    background-color: #FFFFFF;
    border-radius: 12upx;
    padding: 20upx;
    margin-bottom: 20upx;
    box-shadow: 0 2upx 10upx rgba(0, 0, 0, 0.05);
}

.training-image {
    width: 160upx;
    height: 140upx;
    border-radius: 8upx;
    margin-right: 20upx;
    flex-shrink: 0;
}

.training-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.training-title {
    font-size: 30upx;
    color: #333;
    font-weight: 500;
    margin-bottom: 30upx;
    line-height: 1.4;
}

.text-ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 490upx;
}

.training-info {

}

.training-tag {
    display: inline-block;
    font-size: 24upx;
    color: #2D9AFE;
    background-color: #F2F9FF;
    padding: 4upx 16upx;
    transform: translateY(-20upx);
    border-radius: 4upx;
}

.training-stats {
    display: flex;
    align-items: center;
}

.stat-item {
    display: flex;
    align-items: center;
    margin-left: 20upx;
    
    image {
        width: 28upx;
        height: 28upx;
        margin-right: 6upx;
    }
    
    text {
        font-size: 24upx;
        color: #999;
    }
}
.cop {
    text-align: center;
    font-size: 28upx;
    color: #999;
    padding: 30upx 0;
}
</style>
