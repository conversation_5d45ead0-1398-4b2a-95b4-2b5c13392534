<template>
	<view>
		<view class="box">
			<view class="item">
				<view class="left">
					<text>手机号</text>
				</view>
				<view class="input">
					<input placeholder="请输入手机号" v-model="user.mobile" placeholder-class="pla" />
				</view>
			</view>
		</view>	
		<view class="cop"></view>
		<view class="subt" @click="subt">
			<text>提交</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				uid: uni.getStorageSync('uid') || 0,
				user: {},
			}
		},
		onShow() {
			this.$api.get('user/getUser', {
				uid: this.uid
			}).then(res => {
				this.user = res;
			})
		},
		onLoad() {

		},
		methods: {
			isPoneAvailable(t) {
				return !!/^[1][1,3,2,4,5,7,8,9][0-9]{9}$/.test(t);
			},
			subt() {
				if (this.user.mobile == "") {
					this.$api.msg("请输入手机号");
					return false;
				}
				if (!this.isPoneAvailable(this.user.mobile)) {
					this.$api.msg("请输入有效的手机号!");
					return false;
				}
				this.$api.post('user/updateUser?uid='+this.user.id, {
					data: this.user
				}).then(res => {
					if(res == -1){
						this.$api.msg("该手机号码已经被注册了！");
					}else{
						this.$api.msg("绑定成功");
						setTimeout(() => {
							this.$api.tourl('/pages/user/user')
						},2000)
					}
				})
			},
		}
	}
</script>

<style lang="scss">
	page {
		font-family: 'PingFang'; 
	}
	
	.u-radio-group{
		position: relative;
		transform: translateX(20upx);
		.u-radio{
			&:last-of-type{
				position: absolute;
				left: 120upx;		
			}
		}
	}
	.box {
		background-color: #fff;
		width: 94%;
		margin-left: 3%;
		margin-top: 20rpx;
		border-radius: 20upx;
		font-family: PingFang SC, PingFang SC;

		.item {
			display: flex;
			padding: 30upx;
			margin-bottom: 30upx;
			border-bottom: 1px solid #F0F0F0;
			justify-content: space-between;
			align-items: center;

			.left {
				width: 240upx;
				font-weight: 500;
				font-size: 32upx;
				color: #333333;
			}

			.input {
				text-align: center;
				font-size: 32upx;
				color: #333333;

				input {
					font-size: 32upx;
					color: #333333;
					border: 2rpx solid #333;
					padding: 10rpx 20rpx;
					border-radius: 80rpx;
					// box-sizing: bor;
				}

				.pla {
					font-weight: 400;
					font-size: 32upx;
					color: #999999;
				}

				radio {
					transform: scale(.72);
					padding-left: 20upx;
				}
			}

			&:last-of-type {
				border-bottom: none;
			}
		}
	}

	page {
		font-family: PingFang SC, PingFang SC;
		background-color: #F7F8FA;
	}

	.subt {
		background: #2695FF;
		border-radius: 47px 47px 47px 47px;
		text-align: center;
		width: 94%;
		margin-left: 3%;
		height: 90upx;
		line-height: 90upx;
		color: #fff;
		font-size: 32upx;
		position: fixed;
		z-index: 1;
		bottom: 20rpx;
	}
</style>