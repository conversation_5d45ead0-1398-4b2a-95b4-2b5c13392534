(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/files"],{"490a":function(n,t,e){"use strict";(function(n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var e={name:"files",props:{files:{type:Array,default:[]}},data:function(){return{}},methods:{down:function(t){if(!t)return!1;console.log(t);var e=t.split(".");"zip"==e[e.length-1]?n.setClipboardData({data:t,success:function(){n.getClipboardData({success:function(t){n.showToast({icon:"none",title:"复制成功,请打开浏览器进行粘贴下载！"})}})}}):location.href=t}}};t.default=e}).call(this,e("df3c")["default"])},5765:function(n,t,e){},"6dd0":function(n,t,e){"use strict";var o=e("5765"),u=e.n(o);u.a},b600:function(n,t,e){"use strict";e.r(t);var o=e("e718"),u=e("b602");for(var i in u)["default"].indexOf(i)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(i);e("6dd0");var c=e("828b"),a=Object(c["a"])(u["default"],o["b"],o["c"],!1,null,"7695dcca",null,!1,o["a"],void 0);t["default"]=a.exports},b602:function(n,t,e){"use strict";e.r(t);var o=e("490a"),u=e.n(o);for(var i in o)["default"].indexOf(i)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(i);t["default"]=u.a},e718:function(n,t,e){"use strict";e.d(t,"b",(function(){return u})),e.d(t,"c",(function(){return i})),e.d(t,"a",(function(){return o}));var o={uIcon:function(){return Promise.all([e.e("common/vendor"),e.e("uview-ui/components/u-icon/u-icon")]).then(e.bind(null,"22c1"))}},u=function(){var n=this.$createElement;this._self._c},i=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/files-create-component',
    {
        'components/files-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("b600"))
        })
    },
    [['components/files-create-component']]
]);
