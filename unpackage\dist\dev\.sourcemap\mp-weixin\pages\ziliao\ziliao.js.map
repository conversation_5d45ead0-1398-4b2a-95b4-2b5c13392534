{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/kecheng_v3/pages/ziliao/ziliao.vue?b83b", "webpack:///D:/work/kecheng_v3/pages/ziliao/ziliao.vue?3eef", "webpack:///D:/work/kecheng_v3/pages/ziliao/ziliao.vue?e4fd", "webpack:///D:/work/kecheng_v3/pages/ziliao/ziliao.vue?c44d", "uni-app:///pages/ziliao/ziliao.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Footer", "data", "mark", "cname", "cats", "list", "ck", "nodata", "lx", "page", "tab", "onLoad", "onReachBottom", "methods", "tourlIxun", "location", "getData", "setc2", "chong<PERSON>", "setC", "setHide", "butt", "setchild"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACe;;;AAGpE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,gnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCoEvnB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;QACAC;QACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QAAA;QAAA;QAAA;MAAA;QACA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/ziliao/ziliao.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/ziliao/ziliao.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./ziliao.vue?vue&type=template&id=3a1a46f4&\"\nvar renderjs\nimport script from \"./ziliao.vue?vue&type=script&lang=js&\"\nexport * from \"./ziliao.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ziliao.scss?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/ziliao/ziliao.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ziliao.vue?vue&type=template&id=3a1a46f4&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ziliao.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ziliao.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\r\n\t\t<view class=\"navs\">\r\n\t\t\t<text v-for=\"(c,k) in cats\" @click=\"setc2(c.name,c.tab)\" :class=\"c.name == cname ? 'act' : ''\">{{c.name}}</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"box\" v-for=\"(c,k) in list\" @click=\"tourlIxun(c)\">\r\n\t\t\t<text class=\"text1\">{{c.bt}}</text>\r\n\t\t\t<view class=\"sf\">\r\n\t\t\t\t<text class=\"bt\">{{c.lx}}</text>\r\n\t\t\t\t<view class=\"sft\">\r\n\t\t\t\t\t<view class=\"sdd\">\r\n\t\t\t\t\t\t<image src=\"/static/14.png\"></image>\r\n\t\t\t\t\t\t<text>{{c.views}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"sdd\">\r\n\t\t\t\t\t\t<image src=\"/static/15.png\"></image>\r\n\t\t\t\t\t\t<text>{{c.optdt}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!--弹窗-->\r\n\t\t<view class=\"mack\" v-if=\"mark\" @click=\"setHide()\"></view>\r\n\t\t<view class=\"shimw\" v-if=\"mark\">\r\n\t\t\t<view class=\"stt\">\r\n\t\t\t\t<text>{{cname}}</text>\r\n\t\t\t\t<u-icon name=\"arrow-right\" color=\"#fff\" size=\"12\"></u-icon>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"xxx\">\r\n\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t<view class=\"items\">\r\n\t\t\t\t\t\t<text v-for=\"(c,k) in cats\" @click=\"setC(c,k)\" :class=\"c.name == cname ? 'act' : ''\">{{c.name}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"fb\" @click=\"chongzhi()\">重置</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"sright\">\r\n\t\t\t\t\t<view class=\"box1\">\r\n\t\t\t\t\t\t<view class=\"item\" v-for=\"(c,k) in cats\" v-if=\"cname == '全部分类' \">\r\n\t\t\t\t\t\t\t<view class=\"stt2\" style=\"font-size: 12px;\">{{c.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"ul\">\r\n\t\t\t\t\t\t\t\t<text v-for=\"(c2,k2) in c.child\" :class=\"lx == c2.lx ? 'on' : ''\" @click=\"setchild(c2.lx,c.tab)\">{{c2.lx}}</text>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item\"  v-if=\"cname != '全部分类' \">\r\n\t\t\t\t\t\t\t<view class=\"stt2\" style=\"font-size: 12px;\">{{cname}}</view>\r\n\t\t\t\t\t\t\t<view class=\"ul\">\r\n\t\t\t\t\t\t\t\t<text v-for=\"(c2,k2) in cats[ck].child\" :class=\"lx == c2.lx ? 'on' : ''\" @click=\"setchild(c2.lx,cats[ck].tab)\">{{c2.lx}}</text>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"butt\" @click=\"butt()\"><text>确定</text></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!--弹窗-->\r\n\t\t\r\n\t\t<view class=\"cop\">{{nodata}}</view>\n\t\t<view class=\"cop\"></view>\r\n\t\t<Footer :act=\"''\"></Footer>\n\t</view>\n</template>\n\n<script>\n\timport Footer from \"@/components/footer.vue\";\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tFooter\r\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tmark:false,\r\n\t\t\t\tcname:\"全部分类\",\r\n\t\t\t\tcats:[],\r\n\t\t\t\tlist:[],\r\n\t\t\t\tck: 0,\r\n\t\t\t\tnodata:\"\",\r\n\t\t\t\tlx:\"\",\r\n\t\t\t\tpage:1,\r\n\t\t\t\ttab:\"\",\n\t\t\t}\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\tthis.cname = \"全部分类\";\r\n\t\t\tthis.tab = e.tab || \"\";\r\n\t\t\tthis.$api.msg(\"数据读取中...\");\r\n\t\t\tthis.$api.get('zixun/getCats').then(res => {\r\n\t\t\t\tthis.cats = res;\r\n\t\t\t})\r\n\t\t\tif(this.tab == ''){\r\n\t\t\t\tthis.mark = true;\r\n\t\t\t}else if(this.tab != 'all'){\r\n\t\t\t\tif(this.tab == 'xljy'){\r\n\t\t\t\t\tthis.cname = \"学历教育\";\r\n\t\t\t\t}else if(this.tab == 'zcps'){\r\n\t\t\t\t\tthis.cname = \"职称评审\";\r\n\t\t\t\t}else if(this.tab == 'zyzg'){\r\n\t\t\t\t\tthis.cname = \"职业资格\";\r\n\t\t\t\t}else if(this.tab == 'zgzs'){\r\n\t\t\t\t\tthis.cname = \"资格证书\";\r\n\t\t\t\t}else if(this.tab == 'qypx'){\r\n\t\t\t\t\tthis.cname = \"企业培训\";\r\n\t\t\t\t}\r\n\t\t\t}else{\r\n\t\t\t\tthis.setHide();\r\n\t\t\t}\r\n\t\t\tthis.getData();\r\n\t\t},\r\n\t\tonReachBottom(){\r\n\t\t\tif(this.nodata == \"\"){\r\n\t\t\t\tthis.page += 1;\r\n\t\t\t\tthis.$api.msg(\"数据读取中...\");\r\n\t\t\t\tthis.getData();\r\n\t\t\t}\r\n\t\t},\n\t\tmethods: {\r\n\t\t\ttourlIxun(res){\r\n\t\t\t\t//'/pages/news/show?id='+c.id\r\n\t\t\t\tif(res && (res.ljdz && (res.ljdz != ''))){\r\n\t\t\t\t\tlocation.href = res.ljdz;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.$api.tourl('/pages/show/show?id='+res.id+'&tab='+res.s1)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetData(){\r\n\t\t\t\tthis.$api.get('zixun/getList' , {\"tab\":this.tab,\"page\":this.page,\"lx\":this.lx}).then(res => {\r\n\t\t\t\t\t//this.list = res;\r\n\t\t\t\t\tif(!res || (res.length == 0)){\r\n\t\t\t\t\t\tthis.nodata = \"暂无更多数据\";\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.list = this.page == 1 ? res : this.list.concat(res);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsetc2(sc,tab){\r\n\t\t\t\tthis.cname = sc;\r\n\t\t\t\tthis.tab = tab;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.nodata = \"\";\r\n\t\t\t\tif(sc == '全部分类'){\r\n\t\t\t\t\tthis.mark = true;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.$api.msg(\"数据读取中...\");\r\n\t\t\t\t\tthis.getData();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tchongzhi(){\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.nodata = \"\";\r\n\t\t\t\tthis.$api.msg(\"数据读取中...\");\r\n\t\t\t\tthis.lx = \"\";\r\n\t\t\t\tthis.tab = \"\";\r\n\t\t\t\tthis.getData();\r\n\t\t\t\tthis.setHide();\r\n\t\t\t},\r\n\t\t\tsetC(cat,k){\r\n\t\t\t\tthis.cname = cat.name;\r\n\t\t\t\tthis.ck = k;\r\n\t\t\t\tthis.lx = \"\";\r\n\t\t\t\tthis.tab = \"\";\r\n\t\t\t\t//console.log(this.cats[k]);\r\n\t\t\t},\n\t\t\tsetHide(){\r\n\t\t\t\tthis.mark = !this.mark;\r\n\t\t\t},\r\n\t\t\tbutt(){\r\n\t\t\t\tif(this.lx ==  \"\"){\r\n\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\tthis.$api.msg(\"数据读取中...\");\r\n\t\t\t\t\tthis.tab = this.cats[this.ck].tab;\r\n\t\t\t\t\tthis.getData();\r\n\t\t\t\t}\r\n\t\t\t\tthis.setHide();\r\n\t\t\t},\r\n\t\t\tsetchild(lx , tab){\r\n\t\t\t\tthis.lx = lx;\r\n\t\t\t\tthis.tab = tab;\r\n\t\t\t\tthis.$api.msg(\"数据读取中...\");\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.nodata = \"\";\r\n\t\t\t\tthis.getData();\r\n\t\t\t\t//this.setHide();\r\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" src=\"./ziliao.scss\"></style>\n"], "sourceRoot": ""}