{"version": 3, "sources": ["webpack:///D:/work/kecheng_v3/components/jyf-parser/jyf-parser.vue?666c", "webpack:///D:/work/kecheng_v3/components/jyf-parser/jyf-parser.vue?4f01", "webpack:///D:/work/kecheng_v3/components/jyf-parser/jyf-parser.vue?483b", "webpack:///D:/work/kecheng_v3/components/jyf-parser/jyf-parser.vue?52f9", "uni-app:///components/jyf-parser/jyf-parser.vue", "webpack:///D:/work/kecheng_v3/components/jyf-parser/jyf-parser.vue?f468", "webpack:///D:/work/kecheng_v3/components/jyf-parser/jyf-parser.vue?0f32"], "names": ["fs", "<PERSON><PERSON><PERSON>", "val", "name", "data", "showAm", "nodes", "components", "trees", "props", "html", "autopause", "type", "default", "autoscroll", "autosetTitle", "compress", "loadingImg", "useCache", "domain", "lazyLoad", "selectable", "tagStyle", "showWithAnimation", "useAnchor", "watch", "created", "newSrc", "info", "filePath", "encoding", "success", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "<PERSON><PERSON><PERSON><PERSON>", "cache", "uni", "title", "cs", "f", "select", "height", "getText", "replace", "in", "navigateTo", "d", "selector", "scrollTop", "duration", "obj", "getVideoContext"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAumB,CAAgB,onBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACsB3nB;EAEAA;EAEAC;AACA;AACA;AACA;EACA;IACAC;EAAA;EACA;AACA;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,eA0BA;EACAC;EACAC;IACA;MAQAC;MAEAC;IACA;EACA;EAEAC;IACAC;EACA;EAEAC;IACAC;IACAC;MACAC;MACAC;IACA;IACAC;IACAC;MACAH;MACAC;IACA;IAEAG;IACAC;IACAC;IAEAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAf;MACA;IACA;EACA;EACAgB;IACA;IACA;IACA;MACA;QACA;MAAA;IACA;IACA;MAAA;MACA;;MAEA;MACA;QACA;QACA;UACA;UACAC;QACA;QACAA;QACA;MACA;MAEA;MACA;MACA;QACA;UAAAC;QACA;QAEAC;QACA7B;UACA6B;UACAzB;UACA0B;UACAC;YAAA;UAAA;QACA;MAYA;IACA;EACA;EACAC;IAKA;IAMA;EAIA;EACAC;IAIA;MASA,iDACAjC;QACA6B;MACA;IAEA;IACAK;EACA;EACAC;IACA;IACAC;MAAA;MAsLA;MACA;MACA;MACA;MACA;QACA;QACA,oBACA9B,4BACA;UACAA;UACA+B;QACA;MACA;MACA;MACA,uDACA;MACA,sDACAC;QACAC;MACA;MACA;MACA;MACA;QACA;UACA;YACA;cACAC;cACAA;cACAC;YACA;UACA;QACA;QACA;MACA;MAEA;MACAP;MACA;QAKAI,qCACAI;UACA;UACA;UAEA;YACA;YACAR;UACA;UACAS;QAEA;MAEA;MACA;IAEA;IACA;IACAC;MAAA;MACA;MAQA;QACA,2GACAC,4BACA,qCACA;UACA;UACA,oHACA;UACA;UACA;UACA,2DACA;QACA;MACA;MAEA;IACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MAUA;MAEAC;MAEA,oHACA;MACA,oHACAC;MACAA;QACA;QACA;QACA,uEACAX;UACAY;UACAC;QACA;QACAC;MACA;IAEA;IACA;IACAC;MAEA,wCAEA;QACA;MAAA;IAEA;EA4FA;AACA;AAAA,2B;;;;;;;;;;;;;ACnmBA;AAAA;AAAA;AAAA;AAA03B,CAAgB,y2BAAG,EAAC,C;;;;;;;;;;;ACA94B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/jyf-parser/jyf-parser.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./jyf-parser.vue?vue&type=template&id=eab15eb8&\"\nvar renderjs\nimport script from \"./jyf-parser.vue?vue&type=script&lang=js&\"\nexport * from \"./jyf-parser.vue?vue&type=script&lang=js&\"\nimport style0 from \"./jyf-parser.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/jyf-parser/jyf-parser.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jyf-parser.vue?vue&type=template&id=eab15eb8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.nodes.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jyf-parser.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jyf-parser.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<slot v-if=\"!nodes.length\" />\r\n\t\t<!--#ifdef APP-PLUS-NVUE-->\r\n\t\t<web-view id=\"_top\" ref=\"web\" :style=\"'margin-top:-2px;height:'+height+'px'\" @onPostMessage=\"_message\" />\r\n\t\t<!--#endif-->\r\n\t\t<!--#ifndef APP-PLUS-NVUE-->\r\n\t\t<view id=\"_top\" :style=\"showAm+(selectable?';user-select:text;-webkit-user-select:text':'')\">\r\n\t\t\t<!--#ifdef H5 || MP-360-->\r\n\t\t\t<div :id=\"'rtf'+uid\"></div>\r\n\t\t\t<!--#endif-->\r\n\t\t\t<!--#ifndef H5 || MP-360-->\r\n\t\t\t<trees :nodes=\"nodes\" :lazyLoad=\"lazyLoad\" :loading=\"loadingImg\" />\r\n\t\t\t<!--#endif-->\r\n\t\t</view>\r\n\t\t<!--#endif-->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// #ifndef H5 || APP-PLUS-NVUE || MP-360\r\n\timport trees from './libs/trees';\r\n\tvar cache = {},\r\n\t\t// #ifdef MP-WEIXIN || MP-TOUTIAO\r\n\t\tfs = uni.getFileSystemManager ? uni.getFileSystemManager() : null,\r\n\t\t// #endif\r\n\t\tParser = require('./libs/MpHtmlParser.js');\r\n\tvar dom;\r\n\t// 计算 cache 的 key\r\n\tfunction hash(str) {\r\n\t\tfor (var i = str.length, val = 5381; i--;)\r\n\t\t\tval += (val << 5) + str.charCodeAt(i);\r\n\t\treturn val;\r\n\t}\r\n\t// #endif\r\n\t// #ifdef H5 || APP-PLUS-NVUE || MP-360\r\n\tvar {\r\n\t\twindowWidth,\r\n\t\tplatform\r\n\t} = uni.getSystemInfoSync(),\r\n\t\tcfg = require('./libs/config.js');\r\n\t// #endif\r\n\t// #ifdef APP-PLUS-NVUE\r\n\tvar weexDom = weex.requireModule('dom');\r\n\t// #endif\r\n\t/**\r\n\t * Parser 富文本组件\r\n\t * @tutorial https://github.com/jin-yufeng/Parser\r\n\t * @property {String} html 富文本数据\r\n\t * @property {Boolean} autopause 是否在播放一个视频时自动暂停其他视频\r\n\t * @property {Boolean} autoscroll 是否自动给所有表格添加一个滚动层\r\n\t * @property {Boolean} autosetTitle 是否自动将 title 标签中的内容设置到页面标题\r\n\t * @property {Number} compress 压缩等级\r\n\t * @property {String} domain 图片、视频等链接的主域名\r\n\t * @property {Boolean} lazyLoad 是否开启图片懒加载\r\n\t * @property {String} loadingImg 图片加载完成前的占位图\r\n\t * @property {Boolean} selectable 是否开启长按复制\r\n\t * @property {Object} tagStyle 标签的默认样式\r\n\t * @property {Boolean} showWithAnimation 是否使用渐显动画\r\n\t * @property {Boolean} useAnchor 是否使用锚点\r\n\t * @property {Boolean} useCache 是否缓存解析结果\r\n\t * @event {Function} parse 解析完成事件\r\n\t * @event {Function} load dom 加载完成事件\r\n\t * @event {Function} ready 所有图片加载完毕事件\r\n\t * @event {Function} error 错误事件\r\n\t * @event {Function} imgtap 图片点击事件\r\n\t * @event {Function} linkpress 链接点击事件\r\n\t * <AUTHOR>\r\n\t * @version 20200828\r\n\t * @listens MIT\r\n\t */\r\n\texport default {\r\n\t\tname: 'parser',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// #ifdef H5 || MP-360\r\n\t\t\t\tuid: this._uid,\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-PLUS-NVUE\r\n\t\t\t\theight: 1,\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-PLUS-NVUE\r\n\t\t\t\tshowAm: '',\r\n\t\t\t\t// #endif\r\n\t\t\t\tnodes: []\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #ifndef H5 || APP-PLUS-NVUE || MP-360\r\n\t\tcomponents: {\r\n\t\t\ttrees\r\n\t\t},\r\n\t\t// #endif\r\n\t\tprops: {\r\n\t\t\thtml: String,\r\n\t\t\tautopause: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tautoscroll: Boolean,\r\n\t\t\tautosetTitle: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// #ifndef H5 || APP-PLUS-NVUE || MP-360\r\n\t\t\tcompress: Number,\r\n\t\t\tloadingImg: String,\r\n\t\t\tuseCache: Boolean,\r\n\t\t\t// #endif\r\n\t\t\tdomain: String,\r\n\t\t\tlazyLoad: Boolean,\r\n\t\t\tselectable: Boolean,\r\n\t\t\ttagStyle: Object,\r\n\t\t\tshowWithAnimation: Boolean,\r\n\t\t\tuseAnchor: Boolean\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\thtml(html) {\r\n\t\t\t\tthis.setContent(html);\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// 图片数组\r\n\t\t\tthis.imgList = [];\r\n\t\t\tthis.imgList.each = function(f) {\r\n\t\t\t\tfor (var i = 0, len = this.length; i < len; i++)\r\n\t\t\t\t\tthis.setItem(i, f(this[i], i, this));\r\n\t\t\t}\r\n\t\t\tthis.imgList.setItem = function(i, src) {\r\n\t\t\t\tif (i == void 0 || !src) return;\r\n\t\t\t\t// #ifndef MP-ALIPAY || APP-PLUS\r\n\t\t\t\t// 去重\r\n\t\t\t\tif (src.indexOf('http') == 0 && this.includes(src)) {\r\n\t\t\t\t\tvar newSrc = src.split('://')[0];\r\n\t\t\t\t\tfor (var j = newSrc.length, c; c = src[j]; j++) {\r\n\t\t\t\t\t\tif (c == '/' && src[j - 1] != '/' && src[j + 1] != '/') break;\r\n\t\t\t\t\t\tnewSrc += Math.random() > 0.5 ? c.toUpperCase() : c;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tnewSrc += src.substr(j);\r\n\t\t\t\t\treturn this[i] = newSrc;\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis[i] = src;\r\n\t\t\t\t// 暂存 data src\r\n\t\t\t\tif (src.includes('data:image')) {\r\n\t\t\t\t\tvar filePath, info = src.match(/data:image\\/(\\S+?);(\\S+?),(.+)/);\r\n\t\t\t\t\tif (!info) return;\r\n\t\t\t\t\t// #ifdef MP-WEIXIN || MP-TOUTIAO\r\n\t\t\t\t\tfilePath = `${wx.env.USER_DATA_PATH}/${Date.now()}.${info[1]}`;\r\n\t\t\t\t\tfs && fs.writeFile({\r\n\t\t\t\t\t\tfilePath,\r\n\t\t\t\t\t\tdata: info[3],\r\n\t\t\t\t\t\tencoding: info[2],\r\n\t\t\t\t\t\tsuccess: () => this[i] = filePath\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\tfilePath = `_doc/parser_tmp/${Date.now()}.${info[1]}`;\r\n\t\t\t\t\tvar bitmap = new plus.nativeObj.Bitmap();\r\n\t\t\t\t\tbitmap.loadBase64Data(src, () => {\r\n\t\t\t\t\t\tbitmap.save(filePath, {}, () => {\r\n\t\t\t\t\t\t\tbitmap.clear()\r\n\t\t\t\t\t\t\tthis[i] = filePath;\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// #ifdef H5 || MP-360\r\n\t\t\tthis.document = document.getElementById('rtf' + this._uid);\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef H5 || APP-PLUS-NVUE || MP-360\r\n\t\t\tif (dom) this.document = new dom(this);\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef APP-PLUS-NVUE\r\n\t\t\tthis.document = this.$refs.web;\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\t// #endif\r\n\t\t\t\tif (this.html) this.setContent(this.html);\r\n\t\t\t\t// #ifdef APP-PLUS-NVUE\r\n\t\t\t}, 30)\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tbeforeDestroy() {\r\n\t\t\t// #ifdef H5 || MP-360\r\n\t\t\tif (this._observer) this._observer.disconnect();\r\n\t\t\t// #endif\r\n\t\t\tthis.imgList.each(src => {\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tif (src && src.includes('_doc')) {\r\n\t\t\t\t\tplus.io.resolveLocalFileSystemURL(src, entry => {\r\n\t\t\t\t\t\tentry.remove();\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef MP-WEIXIN || MP-TOUTIAO\r\n\t\t\t\tif (src && src.includes(uni.env.USER_DATA_PATH))\r\n\t\t\t\t\tfs && fs.unlink({\r\n\t\t\t\t\t\tfilePath: src\r\n\t\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t})\r\n\t\t\tclearInterval(this._timer);\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 设置富文本内容\r\n\t\t\tsetContent(html, append) {\r\n\t\t\t\t// #ifdef APP-PLUS-NVUE\r\n\t\t\t\tif (!html)\r\n\t\t\t\t\treturn this.height = 1;\r\n\t\t\t\tif (append)\r\n\t\t\t\t\tthis.$refs.web.evalJs(\"var b=document.createElement('div');b.innerHTML='\" + html.replace(/'/g, \"\\\\'\") +\r\n\t\t\t\t\t\t\"';document.getElementById('parser').appendChild(b)\");\r\n\t\t\t\telse {\r\n\t\t\t\t\thtml =\r\n\t\t\t\t\t\t'<meta charset=\"utf-8\" /><meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no\"><style>html,body{width:100%;height:100%;overflow:hidden}body{margin:0}</style><base href=\"' +\r\n\t\t\t\t\t\tthis.domain + '\"><div id=\"parser\"' + (this.selectable ? '>' : ' style=\"user-select:none\">') + this._handleHtml(html).replace(/\\n/g, '\\\\n') +\r\n\t\t\t\t\t\t'</div><script>\"use strict\";function e(e){if(window.__dcloud_weex_postMessage||window.__dcloud_weex_){var t={data:[e]};window.__dcloud_weex_postMessage?window.__dcloud_weex_postMessage(t):window.__dcloud_weex_.postMessage(JSON.stringify(t))}}document.body.onclick=function(){e({action:\"click\"})},' +\r\n\t\t\t\t\t\t(this.showWithAnimation ? 'document.body.style.animation=\"_show .5s\",' : '') +\r\n\t\t\t\t\t\t'setTimeout(function(){e({action:\"load\",text:document.body.innerText,height:document.getElementById(\"parser\").scrollHeight})},50);\\x3c/script>';\r\n\t\t\t\t\tif (platform == 'android') html = html.replace(/%/g, '%25');\r\n\t\t\t\t\tthis.$refs.web.evalJs(\"document.write('\" + html.replace(/'/g, \"\\\\'\") + \"');document.close()\");\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs.web.evalJs(\r\n\t\t\t\t\t'var t=document.getElementsByTagName(\"title\");t.length&&e({action:\"getTitle\",title:t[0].innerText});for(var o,n=document.getElementsByTagName(\"style\"),r=1;o=n[r++];)o.innerHTML=o.innerHTML.replace(/body/g,\"#parser\");for(var a,c=document.getElementsByTagName(\"img\"),s=[],i=0==c.length,d=0,l=0,g=0;a=c[l];l++)parseInt(a.style.width||a.getAttribute(\"width\"))>' +\r\n\t\t\t\t\twindowWidth + '&&(a.style.height=\"auto\"),a.onload=function(){++d==c.length&&(i=!0)},a.onerror=function(){++d==c.length&&(i=!0),' + (cfg.errorImg ? 'this.src=\"' + cfg.errorImg + '\",' : '') +\r\n\t\t\t\t\t'e({action:\"error\",source:\"img\",target:this})},a.hasAttribute(\"ignore\")||\"A\"==a.parentElement.nodeName||(a.i=g++,s.push(a.getAttribute(\"original-src\")||a.src||a.getAttribute(\"data-src\")),a.onclick=function(){e({action:\"preview\",img:{i:this.i,src:this.src}})});e({action:\"getImgList\",imgList:s});for(var u,m=document.getElementsByTagName(\"a\"),f=0;u=m[f];f++)u.onclick=function(){var t,o=this.getAttribute(\"href\");if(\"#\"==o[0]){var n=document.getElementById(o.substr(1));n&&(t=n.offsetTop)}return e({action:\"linkpress\",href:o,offset:t}),!1};for(var h,y=document.getElementsByTagName(\"video\"),v=0;h=y[v];v++)h.style.maxWidth=\"100%\",h.onerror=function(){e({action:\"error\",source:\"video\",target:this})}' +\r\n\t\t\t\t\t(this.autopause ? ',h.onplay=function(){for(var e,t=0;e=y[t];t++)e!=this&&e.pause()}' : '') +\r\n\t\t\t\t\t';for(var _,p=document.getElementsByTagName(\"audio\"),w=0;_=p[w];w++)_.onerror=function(){e({action:\"error\",source:\"audio\",target:this})};' +\r\n\t\t\t\t\t(this.autoscroll ? 'for(var T,E=document.getElementsByTagName(\"table\"),B=0;T=E[B];B++){var N=document.createElement(\"div\");N.style.overflow=\"scroll\",T.parentNode.replaceChild(N,T),N.appendChild(T)}' : '') +\r\n\t\t\t\t\t'var x=document.getElementById(\"parser\");clearInterval(window.timer),window.timer=setInterval(function(){i&&clearInterval(window.timer),e({action:\"ready\",ready:i,height:x.scrollHeight})},350)'\r\n\t\t\t\t)\r\n\t\t\t\tthis.nodes = [1];\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5 || MP-360\r\n\t\t\t\tif (!html) {\r\n\t\t\t\t\tif (this.rtf && !append) this.rtf.parentNode.removeChild(this.rtf);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar div = document.createElement('div');\r\n\t\t\t\tif (!append) {\r\n\t\t\t\t\tif (this.rtf) this.rtf.parentNode.removeChild(this.rtf);\r\n\t\t\t\t\tthis.rtf = div;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (!this.rtf) this.rtf = div;\r\n\t\t\t\t\telse this.rtf.appendChild(div);\r\n\t\t\t\t}\r\n\t\t\t\tdiv.innerHTML = this._handleHtml(html, append);\r\n\t\t\t\tfor (var styles = this.rtf.getElementsByTagName('style'), i = 0, style; style = styles[i++];) {\r\n\t\t\t\t\tstyle.innerHTML = style.innerHTML.replace(/body/g, '#rtf' + this._uid);\r\n\t\t\t\t\tstyle.setAttribute('scoped', 'true');\r\n\t\t\t\t}\r\n\t\t\t\t// 懒加载\r\n\t\t\t\tif (!this._observer && this.lazyLoad && IntersectionObserver) {\r\n\t\t\t\t\tthis._observer = new IntersectionObserver(changes => {\r\n\t\t\t\t\t\tfor (let item, i = 0; item = changes[i++];) {\r\n\t\t\t\t\t\t\tif (item.isIntersecting) {\r\n\t\t\t\t\t\t\t\titem.target.src = item.target.getAttribute('data-src');\r\n\t\t\t\t\t\t\t\titem.target.removeAttribute('data-src');\r\n\t\t\t\t\t\t\t\tthis._observer.unobserve(item.target);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\trootMargin: '500px 0px 500px 0px'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tvar _ts = this;\r\n\t\t\t\t// 获取标题\r\n\t\t\t\tvar title = this.rtf.getElementsByTagName('title');\r\n\t\t\t\tif (title.length && this.autosetTitle)\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: title[0].innerText\r\n\t\t\t\t\t})\r\n\t\t\t\t// 图片处理\r\n\t\t\t\tthis.imgList.length = 0;\r\n\t\t\t\tvar imgs = this.rtf.getElementsByTagName('img');\r\n\t\t\t\tfor (let i = 0, j = 0, img; img = imgs[i]; i++) {\r\n\t\t\t\t\tif (parseInt(img.style.width || img.getAttribute('width')) > windowWidth)\r\n\t\t\t\t\t\timg.style.height = 'auto';\r\n\t\t\t\t\tvar src = img.getAttribute('src');\r\n\t\t\t\t\tif (this.domain && src) {\r\n\t\t\t\t\t\tif (src[0] == '/') {\r\n\t\t\t\t\t\t\tif (src[1] == '/')\r\n\t\t\t\t\t\t\t\timg.src = (this.domain.includes('://') ? this.domain.split('://')[0] : '') + ':' + src;\r\n\t\t\t\t\t\t\telse img.src = this.domain + src;\r\n\t\t\t\t\t\t} else if (!src.includes('://')) img.src = this.domain + '/' + src;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!img.hasAttribute('ignore') && img.parentElement.nodeName != 'A') {\r\n\t\t\t\t\t\timg.i = j++;\r\n\t\t\t\t\t\t_ts.imgList.push(img.getAttribute('original-src') || img.src || img.getAttribute('data-src'));\r\n\t\t\t\t\t\timg.onclick = function() {\r\n\t\t\t\t\t\t\tvar preview = true;\r\n\t\t\t\t\t\t\tthis.ignore = () => preview = false;\r\n\t\t\t\t\t\t\t_ts.$emit('imgtap', this);\r\n\t\t\t\t\t\t\tif (preview) {\r\n\t\t\t\t\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t\t\t\t\tcurrent: this.i,\r\n\t\t\t\t\t\t\t\t\turls: _ts.imgList\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\timg.onerror = function() {\r\n\t\t\t\t\t\tif (cfg.errorImg)\r\n\t\t\t\t\t\t\t_ts.imgList[this.i] = this.src = cfg.errorImg;\r\n\t\t\t\t\t\t_ts.$emit('error', {\r\n\t\t\t\t\t\t\tsource: 'img',\r\n\t\t\t\t\t\t\ttarget: this\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (_ts.lazyLoad && this._observer && img.src && img.i != 0) {\r\n\t\t\t\t\t\timg.setAttribute('data-src', img.src);\r\n\t\t\t\t\t\timg.removeAttribute('src');\r\n\t\t\t\t\t\tthis._observer.observe(img);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 链接处理\r\n\t\t\t\tvar links = this.rtf.getElementsByTagName('a');\r\n\t\t\t\tfor (var link of links) {\r\n\t\t\t\t\tlink.onclick = function() {\r\n\t\t\t\t\t\tvar jump = true,\r\n\t\t\t\t\t\t\thref = this.getAttribute('href');\r\n\t\t\t\t\t\t_ts.$emit('linkpress', {\r\n\t\t\t\t\t\t\thref,\r\n\t\t\t\t\t\t\tignore: () => jump = false\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tif (jump && href) {\r\n\t\t\t\t\t\t\tif (href[0] == '#') {\r\n\t\t\t\t\t\t\t\tif (_ts.useAnchor) {\r\n\t\t\t\t\t\t\t\t\t_ts.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\tid: href.substr(1)\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} else if (href.indexOf('http') == 0 || href.indexOf('//') == 0)\r\n\t\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t\telse\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: href\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 视频处理\r\n\t\t\t\tvar videos = this.rtf.getElementsByTagName('video');\r\n\t\t\t\t_ts.videoContexts = videos;\r\n\t\t\t\tfor (let video, i = 0; video = videos[i++];) {\r\n\t\t\t\t\tvideo.style.maxWidth = '100%';\r\n\t\t\t\t\tvideo.onerror = function() {\r\n\t\t\t\t\t\t_ts.$emit('error', {\r\n\t\t\t\t\t\t\tsource: 'video',\r\n\t\t\t\t\t\t\ttarget: this\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvideo.onplay = function() {\r\n\t\t\t\t\t\tif (_ts.autopause)\r\n\t\t\t\t\t\t\tfor (let item, i = 0; item = _ts.videoContexts[i++];)\r\n\t\t\t\t\t\t\t\tif (item != this) item.pause();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 音频处理\r\n\t\t\t\tvar audios = this.rtf.getElementsByTagName('audio');\r\n\t\t\t\tfor (var audio of audios)\r\n\t\t\t\t\taudio.onerror = function() {\r\n\t\t\t\t\t\t_ts.$emit('error', {\r\n\t\t\t\t\t\t\tsource: 'audio',\r\n\t\t\t\t\t\t\ttarget: this\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t// 表格处理\r\n\t\t\t\tif (this.autoscroll) {\r\n\t\t\t\t\tvar tables = this.rtf.getElementsByTagName('table');\r\n\t\t\t\t\tfor (var table of tables) {\r\n\t\t\t\t\t\tlet div = document.createElement('div');\r\n\t\t\t\t\t\tdiv.style.overflow = 'scroll';\r\n\t\t\t\t\t\ttable.parentNode.replaceChild(div, table);\r\n\t\t\t\t\t\tdiv.appendChild(table);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (!append) this.document.appendChild(this.rtf);\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.nodes = [1];\r\n\t\t\t\t\tthis.$emit('load');\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(() => this.showAm = '', 500);\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-PLUS-NVUE\r\n\t\t\t\t// #ifndef H5 || MP-360\r\n\t\t\t\tvar nodes;\r\n\t\t\t\tif (!html) return this.nodes = [];\r\n\t\t\t\tvar parser = new Parser(html, this);\r\n\t\t\t\t// 缓存读取\r\n\t\t\t\tif (this.useCache) {\r\n\t\t\t\t\tvar hashVal = hash(html);\r\n\t\t\t\t\tif (cache[hashVal])\r\n\t\t\t\t\t\tnodes = cache[hashVal];\r\n\t\t\t\t\telse {\r\n\t\t\t\t\t\tnodes = parser.parse();\r\n\t\t\t\t\t\tcache[hashVal] = nodes;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else nodes = parser.parse();\r\n\t\t\t\tthis.$emit('parse', nodes);\r\n\t\t\t\tif (append) this.nodes = this.nodes.concat(nodes);\r\n\t\t\t\telse this.nodes = nodes;\r\n\t\t\t\tif (nodes.length && nodes.title && this.autosetTitle)\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: nodes.title\r\n\t\t\t\t\t})\r\n\t\t\t\tif (this.imgList) this.imgList.length = 0;\r\n\t\t\t\tthis.videoContexts = [];\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t(function f(cs) {\r\n\t\t\t\t\t\tfor (var i = cs.length; i--;) {\r\n\t\t\t\t\t\t\tif (cs[i].top) {\r\n\t\t\t\t\t\t\t\tcs[i].controls = [];\r\n\t\t\t\t\t\t\t\tcs[i].init();\r\n\t\t\t\t\t\t\t\tf(cs[i].$children);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})(this.$children)\r\n\t\t\t\t\tthis.$emit('load');\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\tvar height;\r\n\t\t\t\tclearInterval(this._timer);\r\n\t\t\t\tthis._timer = setInterval(() => {\r\n\t\t\t\t\t// #ifdef H5 || MP-360\r\n\t\t\t\t\tthis.rect = this.rtf.getBoundingClientRect();\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef H5 || MP-360\r\n\t\t\t\t\tuni.createSelectorQuery().in(this)\r\n\t\t\t\t\t\t.select('#_top').boundingClientRect().exec(res => {\r\n\t\t\t\t\t\t\tif (!res) return;\r\n\t\t\t\t\t\t\tthis.rect = res[0];\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\tif (this.rect.height == height) {\r\n\t\t\t\t\t\t\t\tthis.$emit('ready', this.rect)\r\n\t\t\t\t\t\t\t\tclearInterval(this._timer);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\theight = this.rect.height;\r\n\t\t\t\t\t\t\t// #ifndef H5 || MP-360\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}, 350);\r\n\t\t\t\tif (this.showWithAnimation && !append) this.showAm = 'animation:_show .5s';\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// 获取文本内容\r\n\t\t\tgetText(ns = this.nodes) {\r\n\t\t\t\tvar txt = '';\r\n\t\t\t\t// #ifdef APP-PLUS-NVUE\r\n\t\t\t\ttxt = this._text;\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5 || MP-360\r\n\t\t\t\ttxt = this.rtf.innerText;\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5 || APP-PLUS-NVUE || MP-360\r\n\t\t\t\tfor (var i = 0, n; n = ns[i++];) {\r\n\t\t\t\t\tif (n.type == 'text') txt += n.text.replace(/&nbsp;/g, '\\u00A0').replace(/&lt;/g, '<').replace(/&gt;/g, '>')\r\n\t\t\t\t\t\t.replace(/&amp;/g, '&');\r\n\t\t\t\t\telse if (n.type == 'br') txt += '\\n';\r\n\t\t\t\t\telse {\r\n\t\t\t\t\t\t// 块级标签前后加换行\r\n\t\t\t\t\t\tvar block = n.name == 'p' || n.name == 'div' || n.name == 'tr' || n.name == 'li' || (n.name[0] == 'h' && n.name[1] >\r\n\t\t\t\t\t\t\t'0' && n.name[1] < '7');\r\n\t\t\t\t\t\tif (block && txt && txt[txt.length - 1] != '\\n') txt += '\\n';\r\n\t\t\t\t\t\tif (n.children) txt += this.getText(n.children);\r\n\t\t\t\t\t\tif (block && txt[txt.length - 1] != '\\n') txt += '\\n';\r\n\t\t\t\t\t\telse if (n.name == 'td' || n.name == 'th') txt += '\\t';\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn txt;\r\n\t\t\t},\r\n\t\t\t// 锚点跳转\r\n\t\t\tin (obj) {\r\n\t\t\t\tif (obj.page && obj.selector && obj.scrollTop) this._in = obj;\r\n\t\t\t},\r\n\t\t\tnavigateTo(obj) {\r\n\t\t\t\tif (!this.useAnchor) return obj.fail && obj.fail('Anchor is disabled');\r\n\t\t\t\t// #ifdef APP-PLUS-NVUE\r\n\t\t\t\tif (!obj.id)\r\n\t\t\t\t\tweexDom.scrollToElement(this.$refs.web);\r\n\t\t\t\telse\r\n\t\t\t\t\tthis.$refs.web.evalJs('var pos=document.getElementById(\"' + obj.id +\r\n\t\t\t\t\t\t'\");if(pos)post({action:\"linkpress\",href:\"#\",offset:pos.offsetTop+' + (obj.offset || 0) + '})');\r\n\t\t\t\tobj.success && obj.success();\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-PLUS-NVUE\r\n\t\t\t\tvar d = ' ';\r\n\t\t\t\t// #ifdef MP-WEIXIN || MP-QQ || MP-TOUTIAO\r\n\t\t\t\td = '>>>';\r\n\t\t\t\t// #endif\r\n\t\t\t\tvar selector = uni.createSelectorQuery().in(this._in ? this._in.page : this).select((this._in ? this._in.selector :\r\n\t\t\t\t\t'#_top') + (obj.id ? `${d}#${obj.id},${this._in?this._in.selector:'#_top'}${d}.${obj.id}` : '')).boundingClientRect();\r\n\t\t\t\tif (this._in) selector.select(this._in.selector).scrollOffset().select(this._in.selector).boundingClientRect();\r\n\t\t\t\telse selector.selectViewport().scrollOffset();\r\n\t\t\t\tselector.exec(res => {\r\n\t\t\t\t\tif (!res[0]) return obj.fail && obj.fail('Label not found')\r\n\t\t\t\t\tvar scrollTop = res[1].scrollTop + res[0].top - (res[2] ? res[2].top : 0) + (obj.offset || 0);\r\n\t\t\t\t\tif (this._in) this._in.page[this._in.scrollTop] = scrollTop;\r\n\t\t\t\t\telse uni.pageScrollTo({\r\n\t\t\t\t\t\tscrollTop,\r\n\t\t\t\t\t\tduration: 300\r\n\t\t\t\t\t})\r\n\t\t\t\t\tobj.success && obj.success();\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// 获取视频对象\r\n\t\t\tgetVideoContext(id) {\r\n\t\t\t\t// #ifndef APP-PLUS-NVUE\r\n\t\t\t\tif (!id) return this.videoContexts;\r\n\t\t\t\telse\r\n\t\t\t\t\tfor (var i = this.videoContexts.length; i--;)\r\n\t\t\t\t\t\tif (this.videoContexts[i].id == id) return this.videoContexts[i];\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// #ifdef H5 || APP-PLUS-NVUE || MP-360\r\n\t\t\t_handleHtml(html, append) {\r\n\t\t\t\tif (!append) {\r\n\t\t\t\t\t// 处理 tag-style 和 userAgentStyles\r\n\t\t\t\t\tvar style = '<style>@keyframes _show{0%{opacity:0}100%{opacity:1}}img{max-width:100%}';\r\n\t\t\t\t\tfor (var item in cfg.userAgentStyles)\r\n\t\t\t\t\t\tstyle += `${item}{${cfg.userAgentStyles[item]}}`;\r\n\t\t\t\t\tfor (item in this.tagStyle)\r\n\t\t\t\t\t\tstyle += `${item}{${this.tagStyle[item]}}`;\r\n\t\t\t\t\tstyle += '</style>';\r\n\t\t\t\t\thtml = style + html;\r\n\t\t\t\t}\r\n\t\t\t\t// 处理 rpx\r\n\t\t\t\tif (html.includes('rpx'))\r\n\t\t\t\t\thtml = html.replace(/[0-9.]+\\s*rpx/g, $ => (parseFloat($) * windowWidth / 750) + 'px');\r\n\t\t\t\treturn html;\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef APP-PLUS-NVUE\r\n\t\t\t_message(e) {\r\n\t\t\t\t// 接收 web-view 消息\r\n\t\t\t\tvar d = e.detail.data[0];\r\n\t\t\t\tswitch (d.action) {\r\n\t\t\t\t\tcase 'load':\r\n\t\t\t\t\t\tthis.$emit('load');\r\n\t\t\t\t\t\tthis.height = d.height;\r\n\t\t\t\t\t\tthis._text = d.text;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'getTitle':\r\n\t\t\t\t\t\tif (this.autosetTitle)\r\n\t\t\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\t\t\ttitle: d.title\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'getImgList':\r\n\t\t\t\t\t\tthis.imgList.length = 0;\r\n\t\t\t\t\t\tfor (var i = d.imgList.length; i--;)\r\n\t\t\t\t\t\t\tthis.imgList.setItem(i, d.imgList[i]);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'preview':\r\n\t\t\t\t\t\tvar preview = true;\r\n\t\t\t\t\t\td.img.ignore = () => preview = false;\r\n\t\t\t\t\t\tthis.$emit('imgtap', d.img);\r\n\t\t\t\t\t\tif (preview)\r\n\t\t\t\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t\t\t\tcurrent: d.img.i,\r\n\t\t\t\t\t\t\t\turls: this.imgList\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'linkpress':\r\n\t\t\t\t\t\tvar jump = true,\r\n\t\t\t\t\t\t\thref = d.href;\r\n\t\t\t\t\t\tthis.$emit('linkpress', {\r\n\t\t\t\t\t\t\thref,\r\n\t\t\t\t\t\t\tignore: () => jump = false\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tif (jump && href) {\r\n\t\t\t\t\t\t\tif (href[0] == '#') {\r\n\t\t\t\t\t\t\t\tif (this.useAnchor)\r\n\t\t\t\t\t\t\t\t\tweexDom.scrollToElement(this.$refs.web, {\r\n\t\t\t\t\t\t\t\t\t\toffset: d.offset\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else if (href.includes('://'))\r\n\t\t\t\t\t\t\t\tplus.runtime.openWeb(href);\r\n\t\t\t\t\t\t\telse\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: href\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'error':\r\n\t\t\t\t\t\tif (d.source == 'img' && cfg.errorImg)\r\n\t\t\t\t\t\t\tthis.imgList.setItem(d.target.i, cfg.errorImg);\r\n\t\t\t\t\t\tthis.$emit('error', {\r\n\t\t\t\t\t\t\tsource: d.source,\r\n\t\t\t\t\t\t\ttarget: d.target\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'ready':\r\n\t\t\t\t\t\tthis.height = d.height;\r\n\t\t\t\t\t\tif (d.ready) uni.createSelectorQuery().in(this).select('#_top').boundingClientRect().exec(res => {\r\n\t\t\t\t\t\t\tthis.rect = res[0];\r\n\t\t\t\t\t\t\tthis.$emit('ready', res[0]);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'click':\r\n\t\t\t\t\t\tthis.$emit('click');\r\n\t\t\t\t\t\tthis.$emit('tap');\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t@keyframes _show {\r\n\t\t0% {\r\n\t\t\topacity: 0;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t}\r\n\r\n\t/* #ifdef MP-WEIXIN */\r\n\t:host {\r\n\t\tdisplay: block;\r\n\t\toverflow: scroll;\r\n\t\t-webkit-overflow-scrolling: touch;\r\n\t}\r\n\r\n\t/* #endif */\r\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jyf-parser.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jyf-parser.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1735872263457\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}