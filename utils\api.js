function getQueryString(href , name) {
	var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
	var r = href.match(reg); 
	if (r != null) {
		return unescape(r[2]);
	} 
	return null;
}
// #ifdef H5
var s = window.location.href || "";   
var s= 'http://njt.nxnjt.com/h5/#/pages/index/index?st=1&uniacid=0&uid=628&puid=0&st=1';
// var s = 'http://47.94.160.83/kecheng/h5/#/pages/index/index?st=1&uniacid=0&uid=1&puid=0&st=1';
var uniacid = 2;
var weburl = "";    
var webu = s.split("h5")[0];
var uid = s.split('uid=')[1].split('&')[0] || 0;
var olduid = uni.getStorageSync("uid") || 0;
if(uid){
	if(!olduid || ((olduid != uid) && olduid)){
		uni.setStorageSync("uid" , uid);
	}
}
const url = webu+'public/index.php/index/'; 
const uploadUrl = webu+'public/index.php/index/upload/index';
export {url,uniacid,uploadUrl};
// #endif

