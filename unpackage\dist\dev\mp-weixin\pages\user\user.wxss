page {
  font-family: <PERSON>Fang SC, <PERSON>Fang SC;
}
.top {
  width: 100%;
  background: url(../../static/user/<EMAIL>);
  background-size: 100% 100%;
}
.top .user {
  padding-top: 50rpx;
  padding-left: 60rpx;
  display: flex;
}
.top .user .logo {
  width: 116rpx;
  height: 116rpx;
  border-radius: 58rpx;
  border: 2rpx solid #FFFFFF;
}
.top .user .slef {
  margin-left: 20rpx;
}
.top .user .slef .sfrit {
  display: flex;
}
.top .user .slef .sfrit .text1 {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  display: inline-block;
  width: 100%;
}
.top .user .slef .sfrit .tag {
  margin-left: 20rpx;
  background: rgba(63, 141, 254, 0.23);
  border-radius: 48rpx;
  font-size: 20rpx;
  padding: 10rpx 20rpx;
  color: #2372F9;
}
.top .user .slef .sff {
  background: linear-gradient(180deg, #54B9FF 0%, #377DFD 100%);
  border-radius: 48rpx;
  margin-top: 10rpx;
  color: #fff;
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.top .user .slef .sff image {
  width: 28rpx;
  height: 28rpx;
}
.top .user .slef .sff text {
  display: inline-block;
  padding-left: 10rpx;
}
.top .nav {
  width: 100%;
  padding: 0 20rpx;
  box-sizing: border-box;
  background-color: #fff;
  overflow: hidden;
  margin-top: 40rpx;
  border-radius: 24rpx;
  margin-bottom: 40rpx;
}
.top .nav .item {
  width: 25%;
  float: left;
  text-align: center;
  padding: 40rpx 0rpx;
}
.top .nav .item text {
  display: block;
  padding-top: 20rpx;
  font-weight: 500;
  font-size: 24rpx;
  color: #333333;
}
.top .nav .item image {
  width: 74rpx;
  height: 74rpx;
}
.banner {
  width: 94%;
  margin-left: 3%;
}
.banner image {
  width: 100%;
  height: 260rpx;
  border-radius: 24rpx;
  margin-top: -20rpx;
}
.bnav {
  width: 93%;
  margin-left: 3%;
  background-color: #FFFFFF;
  overflow: hidden;
  margin-top: 10rpx;
  border-radius: 24rpx;
}
.bnav .item {
  display: flex;
  padding: 20rpx;
  justify-content: space-between;
}
.bnav .item .slg {
  width: 40rpx;
  height: 40rpx;
  -webkit-transform: translateY(10rpx);
          transform: translateY(10rpx);
}
.bnav .item view {
  border-bottom: 1rpx solid #E6E6E6;
  width: 90%;
  padding-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
}
.bnav .item view text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.bnav .item view image {
  width: 28rpx;
  height: 28rpx;
}
.bnav .item:last-of-type view {
  border-bottom: none;
}

