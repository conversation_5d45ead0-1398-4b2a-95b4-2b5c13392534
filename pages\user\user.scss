page {
	font-family: 'PingFang'; 
	background: #F5F7FB;
}

.top{
	width: 100%;
	// padding: 10rpx 20rpx;
	box-sizing: border-box;
	
	.user{
		padding-top: 50upx;
		padding-left: 10rpx;
		display: flex;
		// background: url("/static/user/<EMAIL>");
		// background-size: 100% 100%;
		// background-repeat: no-repeat;
		// background-position: center;
		padding: 40rpx 40rpx 0;
		box-sizing: border-box;
		z-index: 999;
		position: relative;
		
		.beijing {
			width: 100%;
			height: 150rpx;
			position: absolute;
			top: 0;
			left: 0;
		}
		
		.logo{
			width: 120rpx;
			height: 120rpx;
			border-radius: 58upx;
			border: 2upx solid #FFFFFF;
		}
		.slef{
			margin-left: 20upx;
			.sfrit{
				display: flex;
				.text1{
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 40rpx;
					color: #15161A;
					display: inline-block;
					width: 100%;
				}
				.tag{
					margin-top: 10rpx;
					margin-left: 20upx;
					background: rgba(#3F8DFE, .23);
					border-radius: 48upx;
					font-size: 20upx;
					padding: 10upx 20upx;
					color: #2372F9;
				}
			}
			.sff{
				background: linear-gradient( 180deg, #54B9FF 0%, #377DFD 100%);
				border-radius: 48upx;
				margin-top: 10upx;
				color: #fff;
				font-size: 24upx;
				padding: 10upx 20upx;
				
				display: flex;
				align-items: center;
				justify-content: center;
				image{
					width: 28upx;
					height: 28upx;
				}
				text{
					display: inline-block;
					padding-left: 10upx;
				}
			}
		}
	}
	.nav{
		width: 100%;
		// margin-left: 20rpx;
		padding: 0 20rpx;
		box-sizing: border-box;
		// background-color: #fff;
		overflow: hidden;
		margin-top: 40upx;
		border-radius: 24upx;
		margin-bottom: 40rpx;
		.item{
			width: 25%;
			float: left;
			text-align: center;
			padding: 20rpx 0upx;
			
			text{
				display: block;
				padding-top: 20upx;
				font-weight: 500;
				font-size: 24rpx;
				color: #333333;
			}
			image{
				width: 74upx;
				height: 74upx;
			}
		}
	}
}
.banner{
	width: 94%;
	margin-left: 3%;
	// background-color: aqua;
	
	image{
		width: 100%;
		height: 240rpx;
		border-radius: 24upx;
		margin-top: -20upx;
	}
}
.bnav{
	width: 93%;
	margin-left: 3%;
	background-color: #FFFFFF;
	overflow: hidden;
	margin-top: 10upx;
	border-radius: 24upx;
	padding: 20rpx;
	box-sizing: border-box;
	
	.item{
		display: flex;
		margin-top: 30rpx;
		// padding: 20upx;
		// box-sizing: border-box;
		justify-content: space-between;
		border-bottom: 1rpx solid #E6E6E6;
		// align-items: center;
		.slg{
			width: 40upx;
			height: 40upx;
			transform: translateY(10upx);
		}
		view{
			width: 90%;
			padding-bottom: 30rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			text{
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 32rpx;
				color: #222222;
			}
			image{
				width: 25rpx;
				height: 28upx;
			}
			
		}
		&:last-of-type{
			view{
				border-bottom: none;
			}
		}
	}
}