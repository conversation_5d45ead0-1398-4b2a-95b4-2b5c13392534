@charset "UTF-8";
view.data-v-d36ba0c0, scroll-view.data-v-d36ba0c0, swiper-item.data-v-d36ba0c0 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-notice.data-v-d36ba0c0 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.u-notice__left-icon.data-v-d36ba0c0 {
  align-items: center;
  margin-right: 5px;
}
.u-notice__right-icon.data-v-d36ba0c0 {
  margin-left: 5px;
  align-items: center;
}
.u-notice__content.data-v-d36ba0c0 {
  text-align: right;
  flex: 1;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  overflow: hidden;
}
.u-notice__content__text.data-v-d36ba0c0 {
  font-size: 16px;
  color: #f9ae3d;
  padding-left: 100%;
  word-break: keep-all;
  font-family: "宋体";
  font-weight: bold;
  white-space: nowrap;
  -webkit-animation: u-loop-animation-data-v-d36ba0c0 10s linear infinite both;
          animation: u-loop-animation-data-v-d36ba0c0 10s linear infinite both;
  display: flex;
  flex-direction: row;
}
@-webkit-keyframes u-loop-animation-data-v-d36ba0c0 {
0% {
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
}
100% {
    -webkit-transform: translate3d(-100%, 0, 0);
            transform: translate3d(-100%, 0, 0);
}
}
@keyframes u-loop-animation-data-v-d36ba0c0 {
0% {
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
}
100% {
    -webkit-transform: translate3d(-100%, 0, 0);
            transform: translate3d(-100%, 0, 0);
}
}

