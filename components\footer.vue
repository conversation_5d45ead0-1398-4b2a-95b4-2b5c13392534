<template>
	<view class="footer33">
		<view class="itemb" @click="key == 1 ? t2url() : $api.tourl(item.path,2)" v-for="(item,key) in bottom"
			:key='key' :class="act == item.act ? 'act' : ''">
			<image :src="item.act == act ? item.img1 : item.img" mode="aspectFill"></image>
			<text>{{item.uname}}</text>
		</view>
	</view>
</template>

<script>
	export default {
		name: "footer",
		props: {
			act: {
				type: String,
				default: ""
			}
		},
		data() {
			return {
				bottom: [{
						"uname": "首页",
						"act": "index",
						"img": "/static/bottom/index.png",
						"img1": "/static/bottom/index1.png",
						"path": "/pages/index/index"
					},
					{
						"uname": "课程",
						"act": "kecheng",
						"img": "/static/bottom/kecheng.png",
						"img1": "/static/bottom/kecheng1.png",
						"path": "/pages/kecheng/kecheng"
					},
					{
						"uname": "资料",
						"act": "ziliao",
						"img": "/static/bottom/ziliao.png",
						"img1": "/static/bottom/ziliao1.png",
						"path": "/pages/infoziliao/infoziliao"
					},
					{
						"uname": "考试日历",
						"act": "rili",
						"img": "/static/bottom/rili.png",
						"img1": "/static/bottom/rili1.png",
						"path": "/pages/rili/rili"
					},
					{
						"uname": "我的",
						"act": "user",
						"img": "/static/bottom/user.png",
						"img1": "/static/bottom/user1.png",
						"path": "/pages/user/user"
					},
				],
			};
		},
		methods: {
			t2url() {
				var user = uni.getStorageSync('user');
				if (!user.mobile) {
					uni.showModal({
						title: '提示：',
						content: '请先完善个人信息，然后点确定再跳转到修改信息页面！',
						success: (res) => {
							if (res.confirm) {
								this.$api.tourl('/pages/user/editinfo');
							} else if (res.cancel) {
								
							}
						}
					});


				} else {
					/*
					this.$api.get('user/getUserApiUrl', {
						uid: user.id,
						'redirect': 3
					}).then(res => {
						location.href = res;
					})
					*/
				   this.$api.tourl('/pages/kecheng/kecheng');
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.footer33 {
		background: #fff;
		z-index: 10;
		position: fixed;
		width: 100%;
		bottom: 0;
		display: flex;
		padding: 5rpx 0;
		box-sizing: border-box;

		// height: 140upx;
		image {
			width: 60rpx;
			height: 65rpx;
			transform: scale(.8);
		}

		.itemb {
			width: 25%;
			text-align: center;
			padding-top: 10upx;

			&.act {
				text {
					color: #2695ff !important;
					// font-size: 28rpx;
				}
			}

			&:nth-child(2) {
				image {
					transform: scale(.9);
				}
			}
		}

		text {
			display: block;
			font-size: 26upx;
			transform: translateY(-6upx);
			color: #BDBDBD;
			font-weight: bold;
			font-family: "[Alibaba]";
		}


	}
</style>