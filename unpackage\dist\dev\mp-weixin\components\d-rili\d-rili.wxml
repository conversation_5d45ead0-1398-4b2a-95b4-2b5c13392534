<view><view class="riliWrapper"><view class="riliWrapperBox"><block wx:if="{{true}}"><view class="signWrapperCalendar"><view class="signWrapperCalendarBox" style="position:relative;"><image class="sleft" src="/static/sleft.png" data-event-opts="{{[['tap',[['sleft']]]]}}" bindtap="__e"></image><image class="sright" src="/static/sright.png" data-event-opts="{{[['tap',[['sright']]]]}}" bindtap="__e"></image><swiper style="height:700rpx;" duration="200" current="{{slideDataListIndex}}" circular="{{true}}" data-event-opts="{{[['change',[['_onClickSlideApi',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l1}}" wx:for-item="calendar" wx:for-index="indexa" wx:key="indexa"><swiper-item class="swiper-item"><view class="signWrapperCalendarBoxTop">{{''+year+"-"+(month<10?'0'+month:month)+''}}</view><view class="signWrapperCalendarBoxCenter"><view class="signWrapperCalendarBoxCenterBox"><view class="signWrapperCalendarBoxCenterBoxTop"><view class="week-number _div"><block wx:for="{{calendar.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><label style="{{'color:'+(item.g0)+';'}}" class="_span">{{item.$orig}}</label></block></view></view><view class="signWrapperCalendarBoxCenterBoxFooter"><block wx:for="{{dayList}}" wx:for-item="dayTime" wx:for-index="idx" wx:key="idx"><view data-event-opts="{{[['tap',[['alert',['$0'],[[['dayList','',idx]]]]]]]}}" class="each-day" bindtap="__e"><block wx:if="{{day}}"><view class="{{[dayTime!=day+2?'eachDayBox':'eachDayBoxCheck']}}"><view class="{{['eachDayBoxBox',dayTime==day?'aabv':'']}}">{{''+(dayTime?dayTime:'')+''}}<block wx:if="{{dayTime==day}}"><image src="/static/<EMAIL>"></image></block></view></view></block><block wx:if="{{iskaos[idx]&&dayTime!=day}}"><text class="iskaoshi">考试</text></block></view></block></view></view></view></swiper-item></block></swiper></view></view></block></view></view></view>