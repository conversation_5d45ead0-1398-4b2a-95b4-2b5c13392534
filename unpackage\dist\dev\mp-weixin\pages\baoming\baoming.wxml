<view><view class="navs"><text data-event-opts="{{[['tap',[['clickCat',['']]]]]}}" class="{{[''==blx?'act':'']}}" bindtap="__e">全部分类</text><block wx:for="{{baocats}}" wx:for-item="c" wx:for-index="k"><text data-event-opts="{{[['tap',[['clickCat',['$0'],[[['baocats','',k,'lx']]]]]]]}}" class="{{[c.lx==blx?'act':'']}}" bindtap="__e">{{c.lx}}</text></block></view><block wx:for="{{baolist}}" wx:for-item="c" wx:for-index="k"><view class="giid"><image src="{{c.fmtp}}" mode data-event-opts="{{[['tap',[['tonbap',['$0'],[[['baolist','',k,'bmdz']]]]]]]}}" bindtap="__e"></image><view class="slf"><text data-event-opts="{{[['tap',[['tonbap',['$0'],[[['baolist','',k,'bmdz']]]]]]]}}" class="text1" bindtap="__e">{{c.bt}}</text><text data-event-opts="{{[['tap',[['tonbap',['$0'],[[['baolist','',k,'bmdz']]]]]]]}}" class="button" bindtap="__e">立刻报名</text></view></view></block><view class="cop">{{nodata}}</view><view class="cop"></view></view>