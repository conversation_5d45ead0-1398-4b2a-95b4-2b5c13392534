page {
	color: #444;
	overflow-x: hidden;
	overflow-y: scroll;
	background: #f2f2f2;
	position: relative;
}
.page-course{
	padding-bottom: 36upx;
	background: #ffffff;
}
.course-top{
	background: #fff;
	position: relative;
	top: -1px;
	image{
		height: 422upx;
		width: 100%;
	}
}
.course-intr{
	padding-bottom: 20upx;
	background: #f2f2f2;
	.a{
		background: #fff;
		padding: 25upx 30upx;
		text{
			display: block;
			&:first-of-type{
				font-size: 36upx;
				line-height: 48upx;
				color: #333;
			}
			&:last-of-type{
				color: #999999;
				font-size: 26upx;
				line-height: 48upx;
			}
		}
	}
}
.course-tab{
	background: #fff;
	.course-tab-button-c{
		height: 92upx;
		.course-tab-button{
			position: relative;
			border-bottom: 2upx solid #e5e5e5;
			padding: 0 20upx;
			&::before{
				display: table;
				content: "";
				line-height: 0;
			}
			text{
				color: #666666;
				  font-size: 15px;
				  float: left;
				  width: 25%;
				  text-align: center;
				  height: 45px;
				  line-height: 45px;
				  font-weight: bold;
				&.shiting{
					position: absolute;
					  right: 360upx;
					  display: inline-block;
					  height: 34upx;
					  width: 58upx;
					  top:20upx;
					  line-height: 34upx;
					  border-radius: 20upx;
					  font-size: 20upx;
					    background: linear-gradient(94deg, #EC6A6E, #F3886D);
					    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.1);
					    color: #ffffff !important;
					  
				}  
				&.active{
					font-weight: normal;
					position: relative;
					&::after{
						content: ' ';
						  width: 100%;
						  height: 4upx;
						  background: #002147;
						  position: absolute;
						  left: 0px;
						  bottom: 0px;
					}
				}
			}
		}
	}
}
.course-tab-block{
	font-size: 12px;
	.course_list_amin{
		.catalog_title{
			font-size: 30upx;
			  color: #333;
			  padding: 10px 12px; 
		}
		.catalog_cont{
			background-color: #fff;
			overflow: hidden;
			.catalog_course_name {
			  float: left;
			  height: 46px;
			  line-height: 46px;
			  font-size: 28upx;
			  color: #333;
			  width: 71%;
			  padding-left: 30upx;
			  white-space: nowrap;
			  overflow: hidden;
			  text-overflow: ellipsis;
			 
			}
			
			.shiting {
			  min-width: 48px !important;
			  padding: 0;
			  height: 18px;
			  line-height: 18px;
			  font-size: 12px;
			  display: block;
			  float: left;
			  text-indent: 0;
			  text-align: center;
			  border-radius: 9px 9px 9px 9px;
			  position: relative;
			  top: 7px;
			  border: 1px solid transparent !important;
			  background: linear-gradient(94deg, #EC6A6E, #F3886D);
			  *background: #EC6A6E;
			  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.1);
			  color: #ffffff !important;
			}
		}
	}
}
.course-footer{
	position: fixed;
	  bottom: 0px;
	  width: 100%;
	  display: flex;
	.course-main{
		width: 70%;
		height: 110upx;
		background-color: #fff;
		border-top: 0.5px solid #cccccc;
		padding-left: 20upx;
		.course-footer-info{
			padding-top: 16upx;
			color: #f16262;
			  font-size: 16px;
			  padding-left: 15px;
			  margin-right: 10px;
			  text-decoration: none;
			  text{
				  &:first-of-type{
					 font-size: 12px; 
				  }
			  }
		}
		.buynums{
			font-size: 12px;
			  color: #999999;
			  padding-left: 15px;
			  padding-top: 10upx;
		}
	}
	.but{
		background: #be0f34;
		  display: block;
		  text-align: center;
		  font-size: 32upx;
		  line-height: 110upx;
		  color: #fff;
		width: 30%;
	}
}