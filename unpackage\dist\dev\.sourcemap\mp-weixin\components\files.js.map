{"version": 3, "sources": ["webpack:///D:/work/kecheng_v3/components/files.vue?d2b9", "webpack:///D:/work/kecheng_v3/components/files.vue?dbd5", "webpack:///D:/work/kecheng_v3/components/files.vue?51c8", "webpack:///D:/work/kecheng_v3/components/files.vue?11fb", "uni-app:///components/files.vue", "webpack:///D:/work/kecheng_v3/components/files.vue?a686", "webpack:///D:/work/kecheng_v3/components/files.vue?b9e2"], "names": ["name", "props", "files", "type", "default", "data", "methods", "down", "console", "uni", "success", "icon", "title", "location"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAmlB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmBvmB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA,QAEA;EACA;EACAC;IACAC;MACA;QACA;MACA;MACAC;MACA;MACA;QACAC;UACAJ;UACAK;YACAD;cACAC;gBACAD;kBACAE;kBACAC;gBACA;cACA;YACA;UACA;QACA;MAGA;QACAC;MACA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/files.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./files.vue?vue&type=template&id=406432cc&scoped=true&\"\nvar renderjs\nimport script from \"./files.vue?vue&type=script&lang=js&\"\nexport * from \"./files.vue?vue&type=script&lang=js&\"\nimport style0 from \"./files.vue?vue&type=style&index=0&id=406432cc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"406432cc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/files.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./files.vue?vue&type=template&id=406432cc&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./files.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./files.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"files\">\r\n\t\t<view class=\"item\" v-for=\"(c,k) in files\">\r\n\t\t\t<view class=\"sleft\">\r\n\t\t\t\t<image src=\"/static/<EMAIL>\"></image>\r\n\r\n\t\t\t</view>\r\n\t\t\t<view class=\"cen\">\r\n\t\t\t\t<text class=\"text1\">{{c.filename}}</text>\r\n\t\t\t\t<text class=\"size\">{{c.filesizecn}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"srg\" @click=\"down(c.filepath)\">\r\n\t\t\t\t<u-icon name=\"download\" color=\"#333\" size=\"28\"></u-icon>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: \"files\",\r\n\t\tprops: {\r\n\t\t\tfiles: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: []\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tdown(file) {\r\n\t\t\t\tif (!file) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(file);\r\n\t\t\t\tvar a = file.split('.');\r\n\t\t\t\tif (a[a.length - 1] == 'zip') {\r\n\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\tdata:file,\r\n\t\t\t\t\t\tsuccess: () =>{\r\n\t\t\t\t\t\t\tuni.getClipboardData({\r\n\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ticon:\"none\",\r\n\t\t\t\t\t\t\t\t\t\ttitle: \"复制成功,请打开浏览器进行粘贴下载！\",\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlocation.href = file;\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.files {\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 10px 10px 10px 10px;\r\n\t\twidth: 92%;\r\n\t\tmargin-left: 1%;\r\n\t\tpadding: 20upx;\r\n\t\tmargin-top: 100upx;\r\n\r\n\t\t.item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tpadding-bottom: 20upx;\r\n\t\t\tborder-bottom: 1px solid rgba(0, 0, 0, 0.05);\r\n\t\t\tmargin-bottom: 20upx;\r\n\r\n\t\t\t&:last-of-type {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t\tpadding-bottom: 0upx;\r\n\t\t\t}\r\n\r\n\t\t\t.sleft {\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 84upx;\r\n\t\t\t\t\theight: 84upx;\r\n\t\t\t\t\ttransform: translateY(-10upx);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.cen {\r\n\t\t\t\tpadding-left: 20upx;\r\n\t\t\t\tfont-family: PingFang SC, PingFang SC;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tfont-size: 28upx;\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t\twidth: 70%;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tdisplay: block;\r\n\r\n\t\t\t\t\t&:last-of-type {\r\n\t\t\t\t\t\tfont-size: 24upx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\tpadding-top: 10upx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.srg {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./files.vue?vue&type=style&index=0&id=406432cc&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./files.vue?vue&type=style&index=0&id=406432cc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1735872264676\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}