page {
  font-family: <PERSON>Fang SC, PingFang SC;
}
.navs {
  margin-top: 20rpx;
  width: 100%;
  margin-left: 10rpx;
  overflow-x: scroll;
  white-space: nowrap;
}
.navs text {
  display: inline-block;
  white-space: nowrap;
  box-sizing: border-box;
  padding: 10rpx 25rpx;
  background-color: #FFFFFF;
  font-weight: 500;
  font-size: 28rpx;
  color: #555555;
  border-radius: 30rpx;
  margin-right: 30rpx;
}
.navs text.act {
  color: #fff;
  background: linear-gradient(360deg, #2D9AFE 0%, #47B8FF 100%);
}
.box1 {
  margin-top: 20rpx;
  width: 100%;
  padding: 20rpx;
  box-sizing: border-box;
}
.box1 .item {
  background-color: #fff;
  width: 48%;
  float: left;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
}
.box1 .item:nth-child(2n) {
  float: right;
}
.box1 .item .text2 {
  padding: 10rpx 20rpx;
  font-weight: bold;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
}
.box1 .item image {
  width: 100%;
  height: 220rpx;
  border-radius: 20rpx;
}
.box1 .item .firs {
  padding: 0 20rpx 20rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.box1 .item .firs .sdd {
  font-size: 24rpx;
  color: #499EFB;
}
.box1 .item .firs .sdd text:last-of-type {
  color: #999;
}
.box1 .item .firs .bbb {
  background: linear-gradient(360deg, #2D9AFE 0%, #47B8FF 100%);
  border-radius: 66rpx;
  font-weight: bold;
  font-size: 24rpx;
  color: #FFFFFF;
  padding: 12rpx 26rpx;
}

