<view><view class="navs"><text data-event-opts="{{[['tap',[['setCat',['']]]]]}}" class="{{[''==cname?'act':'']}}" bindtap="__e">全部</text><block wx:for="{{cats}}" wx:for-item="c" wx:for-index="k"><text data-event-opts="{{[['tap',[['setCat',['$0'],[[['cats','',k,'lx']]]]]]]}}" class="{{[c.lx==cname?'act':'']}}" bindtap="__e">{{c.lx}}</text></block></view><block wx:for="{{list}}" wx:for-item="c" wx:for-index="k"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({c})}}" class="box" bindtap="__e"><text class="text1">{{c.bt}}</text><view class="sf"><text class="bt">{{c.lx}}</text><view class="sft"><view class="sdd"><image src="/static/14.png"></image><text>{{c.lls}}</text></view><view class="sdd"><image src="/static/15.png"></image><text>{{c.optdt}}</text></view></view></view></view></block><view class="cop">{{nodata}}</view><footer vue-id="1cdd0ce0-1" act="{{act}}" bind:__l="__l"></footer></view>