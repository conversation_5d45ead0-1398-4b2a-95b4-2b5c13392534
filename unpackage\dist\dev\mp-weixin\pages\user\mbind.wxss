.u-radio-group {
  position: relative;
  -webkit-transform: translateX(20rpx);
          transform: translateX(20rpx);
}
.u-radio-group .u-radio:last-of-type {
  position: absolute;
  left: 120rpx;
}
.box {
  background-color: #fff;
  width: 94%;
  margin-left: 3%;
  margin-top: 20rpx;
  border-radius: 20rpx;
  font-family: PingFang SC, PingFang SC;
}
.box .item {
  display: flex;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border-bottom: 1px solid #F0F0F0;
  justify-content: space-between;
  align-items: center;
}
.box .item .left {
  width: 240rpx;
  font-weight: 500;
  font-size: 32rpx;
  color: #333333;
}
.box .item .input {
  text-align: center;
  font-size: 32rpx;
  color: #333333;
}
.box .item .input input {
  font-size: 32rpx;
  color: #333333;
  border: 2rpx solid #333;
  padding: 10rpx 20rpx;
  border-radius: 80rpx;
}
.box .item .input .pla {
  font-weight: 400;
  font-size: 32rpx;
  color: #999999;
}
.box .item .input radio {
  -webkit-transform: scale(0.72);
          transform: scale(0.72);
  padding-left: 20rpx;
}
.box .item:last-of-type {
  border-bottom: none;
}
page {
  font-family: PingFang SC, PingFang SC;
  background-color: #F7F8FA;
}
.subt {
  background: #2695FF;
  border-radius: 47px 47px 47px 47px;
  text-align: center;
  width: 94%;
  margin-left: 3%;
  height: 90rpx;
  line-height: 90rpx;
  color: #fff;
  font-size: 32rpx;
  position: fixed;
  z-index: 1;
  bottom: 20rpx;
}

