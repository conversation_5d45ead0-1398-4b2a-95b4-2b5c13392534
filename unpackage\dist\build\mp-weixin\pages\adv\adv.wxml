<view><view class="top"><view class="title">{{''+row.bt+''}}</view><view class="sdd"><u-icon vue-id="4181dd58-1" name="clock" color="#999" size="14" bind:__l="__l"></u-icon><text>{{row.optdt}}</text></view><view class="box"><view class="d"><jyf-parser class="vue-ref" vue-id="4181dd58-2" html="{{row.nr}}" data-ref="article" bind:__l="__l"></jyf-parser></view></view><view class="adv"><image src="{{adv[0].fmtp}}" data-event-opts="{{[['tap',[['tourl']]]]}}" bindtap="__e"></image></view></view><files vue-id="4181dd58-3" files="{{row.files}}" bind:__l="__l"></files></view>