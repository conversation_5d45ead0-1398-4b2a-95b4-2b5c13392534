<template>
	<view>
		<view class="page-course">
			<view class="course-top">
				<image :src="row.fmtp"></image>
			</view>
			<view class="course-intr">
				<view class="a">
					<text>{{row.bt}}</text>
					<text>有效期：365天 （{{row.video_count}}讲）</text>
				</view>
			</view>
			<view class="course-tab">
				<view class="course-tab-button-c">
					<view class="course-tab-button">
						<text  :class="on == 0 ? 'active' : ''" @click="setOn(0)">课程介绍</text>
						<text  :class="on == 1 ? 'active' : ''" @click="setOn(1)">课程表</text>
						<text class="shiting">{{row.isbuy ? '播放' : (row.video_id ? '试听' : '购买')}}</text>
					</view>
				</view>
			</view>
			<view class="course-tab-block" v-if="on == 0">
				<jyf-parser :html="row.nr" ref="article"></jyf-parser>　
			</view>
			<view class="course-tab-block" v-if="on == 1">
				<view class="course_list_amin">
					<block v-for="(c,k) in row.lclist">
					<view class="catalog_title" style="background: #f5f5f5;">{{c.title}}</view>
						<block v-for="(c2,k2) in c.children">
							<view class="catalog_title">{{c2.title}}</view>
							<block v-for="(c3,k3) in c2.chapter">
								<view class="catalog_title">{{c3.title}}</view>
								<block v-for="(c4,k4) in c3.video_list">
									<view class="catalog_cont first">
										<text class="catalog_course_name" style="margin-left: 40upx;border-bottom: 1px solid #eeeeee;">{{c4.title}}</text>
										<text v-if="c4.is_free && (!row.isbuy)" class="shiting" style="float: right;margin-top: 5px;margin-right: 20px" @click="kan(c4.id)">试听</text>
										<text v-if="row.isbuy" class="shiting" style="float: right;margin-top: 5px;margin-right: 20px" @click="kan(c4.id)">播放</text>
										
									</view>
								</block>
								
							</block>
						</block>
					</block>
				</view>
			</view>
			<view class="course-footer">
				<view class="course-main">
				    <view class="course-footer-info">
						<view class="priceinfo">
							<text>¥</text>
							<text>{{row.gmjg}}</text>
						</view>
					</view>
					<view class="buynums" @click="buykc()">
						 点击立即购买
					</view>
				 </view>
				 <view class="but" @click="buykc()">{{row.isbuy ? '课程已购买' : '立刻购买'}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	import jyfParser from "@/components/jyf-parser/jyf-parser";
	export default {
		components:{jyfParser},
		data() {
			return {
				id:0,
				on:0,
				type:0,
				row:{}
			}
		},
		onLoad(e) {
			this.id = e.id || 2;
			this.type = e.type || 0;
			this.$api.get('kecheng/row',{id:this.id,'uid':uni.getStorageSync('uid')}).then(res => {
				this.row = res;
				if(this.type == 2){
					this.on = 1;
					if(this.row.video_id){
						this.kan(this.row.video_id);
					}
				}
			})
		},
		methods: {
			kan(video_id){
				var user = uni.getStorageSync('user');
				if(!user.mobile){
					uni.showModal({
						title: '提示：',
						content: '请先完善个人信息，然后点确定再跳转到修改信息页面！',
						success: (res) => {
							if (res.confirm) {
								this.$api.tourl('/pages/user/editinfo');
							} else if (res.cancel) {
								
							}
						}
					});
				}else{
					this.$api.tourl('/pages/kecheng/video?id='+this.id+'&video_id='+video_id);
				}
				
			},
			getY(k){
				var a = ['一','二','三','四','五','六','七','八','九','十','十一','十二','十三','十四','十五','十六','十七','十八','十九','二十'];
				return a[k];
			},
			setOn(s){
				this.on = s;
			},
			buykc(){
				var user = uni.getStorageSync('user');
				if(!user.mobile){
					uni.showModal({
						title: '提示：',
						content: '请先完善个人信息，然后点确定再跳转到修改信息页面！',
						success: (res) => {
							if (res.confirm) {
								this.$api.tourl('/pages/user/editinfo');
							} else if (res.cancel) {
								
							}
						}
					});
				}else{
					if(this.row.isbuy){
						this.$api.msg("您已经购买了该课程，无需在线购买!");
					}else{
						this.$api.tourl('/pages/kecheng/orderpay?id='+this.id);
					}
					
				}
			}
		}
	}
</script>

<style lang="scss" src="./show.scss"></style>
