<template>
	<view>
		<view class="b0nav">
			<view class="bnavs">
				<view class="navs" :class="{ 'scrolled': scrollY > 50 }">
					<text v-for="(c,k) in cats" @click="setCat(k)" :class="key == k ? 'act' : ''">{{c.name}}</text>
					<text style="background-color: transparent;width: 30upx;"></text>
				</view>
				<view class="navs2" :class="{ 'scrolled': scrollY > 50 }" v-if="cats.length && cats[key] && cats[key].child">
					<text @click="setCat2('')" :class="lx2 == '' ? 'act' : ''">全部</text>
					<text v-for="(c,k) in cats[key].child" @click="setCat2(c.lx)" :class="lx2 == c.lx ? 'act' : ''">{{c.lx}}</text>
				</view>
				<view v-else class="navs2"></view>
			</view>
		</view>
		<view class="box" v-if="list.length">
			<view class="training-list">
			<view class="training-item" v-for="(c,k) in list" :key="k" @click="tourlIxun(c)">
				<image class="training-image" :src="c.fmtp || '/static/default-building.jpg'"></image>
				<view class="training-content">
					<view class="training-title text-ellipsis">{{ c.bt }}</view>
					<view class="training-info">
						<view class="training-tag">{{c.lx}}</view>
						<view class="training-stats">
							<view class="stat-item">
								<image src="/static/14.png" style="width: 38upx;height: 38upx;"></image>
								<text>{{c.views || ''}}</text>
							</view>
							<view class="stat-item">
								<image src="/static/15.png"></image>
								<text style="padding-left: 10upx;">{{c.optdt || ''}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="cop">{{nodata || "暂无更多数据"}}</view>
		<view class="cop"></view>
		</view>
        
		<Footer :act="''"></Footer>
	</view>
</template>

<script>
	import Footer from "@/components/footer.vue";
	export default {
		components: {
			Footer
		},
		data() {
			return {
				mark:false,
				cname:"全部分类",
				cats:[],
                lx2:"",
                key:0,
				list:[],
				ck: 0,
				nodata:"",
				lx:"",
				page:1,
				tab:1,
				scrollY: 0,
			}
		},
		onLoad(e) {
			
			this.cname = "全部分类";
			this.tab = e.tab || "";
			this.$api.msg("数据读取中...");
			this.$api.get('zixun/getCats').then(res => {
				this.cats = res;
                this.getData();
			})
			if(this.tab == ''){
				this.mark = true;
			}else if(this.tab != 'all'){
				if(this.tab == 'xljy'){
					this.cname = "学历教育";
                    this.key = 1;
				}else if(this.tab == 'zcps'){
					this.cname = "职称评审";
                    this.key = 2;
				}else if(this.tab == 'zyzg'){
					this.cname = "职业资格";
                    this.key = 3;
				}else if(this.tab == 'zgzs'){
					this.cname = "资格证书";
                    this.key = 4;
				}else if(this.tab == 'qypx'){
					this.cname = "企业培训";
                    this.key = 5;
				}
			}else{
				this.setHide();
			}
			uni.setNavigationBarTitle({
				title:"宁教通-"+this.cname
			})
		},
		onPageScroll(e){
			this.scrollY = e.scrollTop;
		},
		onReachBottom(){
			if(this.nodata == ""){
				this.page += 1;
				this.$api.msg("数据读取中...");
				this.getData();
			}
		},
		methods: {
            setCat(k){
			
                this.key = k;
                this.lx2 = "";
                this.cname = this.cats[k].name;
				uni.setNavigationBarTitle({
					title:"宁教通-"+this.cname
				})
				this.list = [];
                this.tab = k;
				this.page = 1;
				this.nodata = "";
				this.getData();
            },
            setCat2(lx){
				
                this.lx2 = lx;
                this.page = 1;
				this.list = [];
				this.nodata = "暂无更多数据";
                this.getData(lx);
            },
			tourlIxun(res){
				if(res && (res.ljdz && (res.ljdz != ''))){
					location.href = res.ljdz;
					return false;
				}else{
					this.$api.tourl('/pages/show/show?id='+res.id+'&tab='+res.s1)
				}
			},
			getData(lx){
                const filterLx = lx || this.lx2;
				this.$api.get('zixun/getList', {
                    "tab": this.tab, 
                    "page": this.page, 
                    "lx": filterLx
                }).then(res => {
					if(!res || (res.length == 0)){
						this.nodata = "暂无更多数据";
                        //this.list = [];
					}else{
						this.list = this.page == 1 ? res : this.list.concat(res);
					}
				})
			}
		}
	}
</script>

<style lang="scss" src="./ziliao.scss"></style>
