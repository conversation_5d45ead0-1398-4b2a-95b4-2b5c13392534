{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/kecheng_v3/pages/kecheng/show.vue?065f", "webpack:///D:/work/kecheng_v3/pages/kecheng/show.vue?1f14", "webpack:///D:/work/kecheng_v3/pages/kecheng/show.vue?67d4", "webpack:///D:/work/kecheng_v3/pages/kecheng/show.vue?b718", "uni-app:///pages/kecheng/show.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "id", "row", "onLoad", "methods", "tourl", "location"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACe;;;AAGlE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAimB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eC0BrnB;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;MAAAF;IAAA;MACA;IACA;EACA;EACAG;IACAC;MACAC;IACA;EACA;AACA;AAAA,2B", "file": "pages/kecheng/show.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/kecheng/show.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./show.vue?vue&type=template&id=d9713f74&\"\nvar renderjs\nimport script from \"./show.vue?vue&type=script&lang=js&\"\nexport * from \"./show.vue?vue&type=script&lang=js&\"\nimport style0 from \"./show.scss?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/kecheng/show.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./show.vue?vue&type=template&id=d9713f74&\"", "var components\ntry {\n  components = {\n    jyfParser: function () {\n      return import(\n        /* webpackChunkName: \"components/jyf-parser/jyf-parser\" */ \"@/components/jyf-parser/jyf-parser.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./show.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./show.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view class=\"top\">\r\n\t\t\t<video :src=\"row.spdz\" style=\"width: 100%;height: 400upx;\"></video>\r\n\t\t</view>\r\n\t\t<view class=\"item-rankxt\">\r\n\t\t\t{{row.bt}}\r\n\t\t</view>\r\n\t\t<view class=\"info\" style=\"display: none;\">\r\n\t\t\t<text>类型：{{row.lx}}</text>\r\n\t\t\t<text>有效期：{{row.kssj}}</text>\r\n\t\t\t<text>是否有效：{{row.yesno}}</text>\r\n\t\t\t<text>价格：{{row.gmjg || \"免费\"}}</text>\r\n\t\t</view>\r\n\t\t<view class=\"nr\">\r\n\t\t\t<view class=\"d\" style=\"padding: 20upx;\">\r\n\t\t\t\t<jyf-parser :html=\"row.nr\" ref=\"article\"></jyf-parser>　\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</view>\r\n\t\t<view class=\"but\" @click=\"tourl()\">课程报名</view>\n\t</view>\n</template>\n\n<script>\r\n\timport jyfParser from \"@/components/jyf-parser/jyf-parser\";\n\texport default {\r\n\t\tcomponents:{jyfParser},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tid:0,\r\n\t\t\t\trow:{}\n\t\t\t}\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\tthis.id = e.id || 2;\r\n\t\t\tthis.$api.get('kecheng/row',{id:this.id}).then(res => {\r\n\t\t\t\tthis.row = res;\r\n\t\t\t})\r\n\t\t},\n\t\tmethods: {\n\t\t\ttourl(){\r\n\t\t\t\tlocation.href = this.row.ljdz;\r\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" src=\"./show.scss\"></style>\n"], "sourceRoot": ""}