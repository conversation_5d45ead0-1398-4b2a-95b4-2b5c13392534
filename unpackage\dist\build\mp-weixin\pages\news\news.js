(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/news/news"],{"1a77":function(t,n,a){"use strict";a.r(n);var e=a("3c93"),i=a.n(e);for(var c in e)["default"].indexOf(c)<0&&function(t){a.d(n,t,(function(){return e[t]}))}(c);n["default"]=i.a},"3c93":function(t,n,a){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e={components:{Footer:function(){a.e("components/footer").then(function(){return resolve(a("ccb8"))}.bind(null,a)).catch(a.oe)}},data:function(){return{cname:"",cats:[],list:[],page:1,act:"ziliao",ck:0,nodata:"",lx:"",tab:""}},onLoad:function(t){var n=this;this.lx=t.lx||"",this.$api.msg("数据读取中..."),this.$api.get("news/cats").then((function(t){n.cats=t})),this.getData(this.lx)},onReachBottom:function(){""==this.nodata&&(this.page+=1,this.$api.msg("数据读取中..."),this.getData(this.cname))},methods:{setCat:function(n){this.page=1,this.nodata="",this.cname=n,""==n?t.setNavigationBarTitle({title:"宁教通-学习资讯"}):t.setNavigationBarTitle({title:"宁教通-"+n}),this.getData(n),this.act="资料"==n?"ziliao":""},getData:function(t){var n=this;this.cname=t,this.$api.get("news/getList",{lx:t,page:this.page}).then((function(t){t&&0!=t.length?n.list=1==n.page?t:n.list.concat(t):n.nodata="暂无更多数据"}))}}};n.default=e}).call(this,a("df3c")["default"])},"5f63":function(t,n,a){"use strict";a.r(n);var e=a("e17f"),i=a("1a77");for(var c in i)["default"].indexOf(c)<0&&function(t){a.d(n,t,(function(){return i[t]}))}(c);a("fd31");var o=a("828b"),s=Object(o["a"])(i["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=s.exports},a830:function(t,n,a){"use strict";(function(t,n){var e=a("47a9");a("c7a0");e(a("3240"));var i=e(a("5f63"));t.__webpack_require_UNI_MP_PLUGIN__=a,n(i.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},e17f:function(t,n,a){"use strict";a.d(n,"b",(function(){return e})),a.d(n,"c",(function(){return i})),a.d(n,"a",(function(){}));var e=function(){var t=this,n=t.$createElement;t._self._c;t._isMounted||(t.e0=function(n,a){var e=arguments[arguments.length-1].currentTarget.dataset,i=e.eventParams||e["event-params"];a=i.c;return t.$api.tourl("/pages/news/show?id="+a.id)})},i=[]}},[["a830","common/runtime","common/vendor"]]]);