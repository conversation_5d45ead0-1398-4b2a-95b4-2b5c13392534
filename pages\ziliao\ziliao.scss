page{
	min-height: 100vh;
	width: 100%;
    
}

.b0nav{
 
}
.bnavs{
    width: 100%;
    background-image: url("../../static/7051.jpg");
    background-size: 100% 100%;
    z-index: 31;
    position: fixed;
    top:  0upx;
    .navs{
        width: 100%;
        height: 72upx;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        padding: 0upx 50upx;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        white-space: nowrap;
        display: flex;
        margin-top: 20upx;
        align-items: center;
        &::-webkit-scrollbar {
            display: none;
        }
        text{
            display: inline-block;
            white-space: nowrap;
            box-sizing:border-box;
            padding: 8rpx 20rpx;
            background: #F2F9FF;
            font-weight: 500;
            font-weight: 500;
            font-size: 28upx;
            color: #666666;
            border-radius: 10rpx;
            margin-right: 30rpx;
            border-radius: 24upx;
            &.act{
                background: #2D9AFE;
                color: #FFFFFF;
            }
        }
    }
    .navs2{
        width: 100%;
        height: 72upx;  
        padding: 20upx 50upx 5upx 50upx;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
      
        scrollbar-color: transparent transparent;
        white-space: nowrap;
        background: transparent;
        &::-webkit-scrollbar {
            display: none;
        }
        &.scrolled{
            
            
        }
        text{
            display: inline-block;
            white-space: nowrap;
            box-sizing:border-box;
            padding: 8rpx 20rpx;
            font-weight: 500;
            font-weight: 500;
            font-size: 28upx;
            color: #666666;
            border-radius: 10rpx;
            margin-right: 30rpx;
            border-radius: 24upx;
            &.act{
                color: #2D9AFE;
            }
        }
    }
    
}

.training-list {
    padding: 0upx 50upx 50upx 50upx;
	transform: translateY(185upx);
    background: linear-gradient(to top, #f5f7fb, #eaeefe);
}

.training-item {
    display: flex;
    background-color: #FFFFFF;
    border-radius: 12upx;
    padding: 20upx;
    margin-bottom: 20upx;
    box-shadow: 0 2upx 10upx rgba(0, 0, 0, 0.05);
}

.training-image {
    width: 160upx;
    height: 140upx;
    border-radius: 8upx;
    margin-right: 20upx;
    flex-shrink: 0;
}

.training-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.training-title {
    font-size: 30upx;
    color: #333;
    font-weight: 500;
    margin-bottom: 30upx;
    line-height: 1.4;
}

.text-ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 490upx;
}

.training-info {

}

.training-tag {
    display: inline-block;
    font-size: 24upx;
    color: #2D9AFE;
    background-color: #F2F9FF;
    padding: 4upx 16upx;
    transform: translateY(-20upx);
    border-radius: 4upx;
}

.training-stats {
    display: flex;
    align-items: center;
}

.stat-item {
    display: flex;
    align-items: center;
    margin-left: 20upx;
    
    image {
        width: 28upx;
        height: 28upx;
        margin-right: 6upx;
    }
    
    text {
        font-size: 24upx;
        color: #999;
    }
}
.cop{
    padding: 20rpx;
}