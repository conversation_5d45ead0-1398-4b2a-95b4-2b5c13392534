{"version": 3, "sources": ["webpack:///D:/work/kecheng_v3/components/jyf-parser/libs/trees.vue?bac7", "webpack:///D:/work/kecheng_v3/components/jyf-parser/libs/trees.vue?e1b8", "webpack:///D:/work/kecheng_v3/components/jyf-parser/libs/trees.vue?7e23", "webpack:///D:/work/kecheng_v3/components/jyf-parser/libs/trees.vue?1d31", "uni-app:///components/jyf-parser/libs/trees.vue", "webpack:///D:/work/kecheng_v3/components/jyf-parser/libs/trees.vue?ce07", "webpack:///D:/work/kecheng_v3/components/jyf-parser/libs/trees.vue?09b6", "webpack:///D:/work/kecheng_v3/components/jyf-parser/libs/handler.wxs?8009", "webpack:///D:/work/kecheng_v3/components/jyf-parser/libs/handler.wxs?1149"], "names": ["global", "components", "trees", "name", "data", "ctrl", "placeholder", "errorImg", "loadVideo", "c", "s", "props", "nodes", "lazyLoad", "loading", "mounted", "methods", "init", "ctx", "play", "contexts", "imgtap", "id", "src", "ignore", "current", "uni", "urls", "loadImg", "linkpress", "attrs", "appId", "path", "success", "title", "url", "fail", "error", "source", "i", "e", "target", "errMsg", "_loadVideo"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsT;AACtT;AACyD;AACL;AACa;;;AAGjE;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,oRAAM;AACR,EAAE,6RAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wRAAU;AACZ;AACA;;AAEA;AACoL;AACpL,WAAW,sMAAM,iBAAiB,8MAAM;;AAExC;AACe,gF;;;;;;;;;;;;AC3Bf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAinB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwEroBA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;IAEA;EACA;EACAC;IACAC;IACAC;IACAC;EAKA;EACAC;IACA;MAAA;IAAA;IACA;EACA;EAMAC;IACAC;MACA;QACA;UACA;QAmBA;UACA;UACA;YACAC,yCAEA,KAEA;UACA,mCACAA;UACA;YACAA;YACA;UACA;QACA;MACA;IAOA;IACAC;MACA;MACA,+CACA;QACA,kDACAC;MAAA;IACA;IACAC;MACA;MACA;QACA;UACAjB;YACAkB;YACAC;YACAC;cAAA;YAAA;UACA;QACAxB;QACA;QACA;UACA;YACAyB;UACAC;YACAD;YACAE;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;QAMA;MAKA;QAKA;MAIA;IACA;IACAC;MACA;QACAC;MACAA;QAAA;MAAA;MACA9B;MACA;MACA;QAEA;UACA;YACA+B;YACAC;UACA;QACA;QAEA;UACA;YACA,wBACA;cACAV;YACA;UACA;YAKAI;cACAtB;cACA6B;gBAAA,OACAP;kBACAQ;gBACA;cAAA;YACA;UAEA,OACAR;YACAS;YACAC;cACAV;gBACAS;cACA;YACA;UACA;QACA;MACA;IACA;IACAE;MACA;QACAC;QACAC;MACA;QACA;QACA;QACA,+CACA;QACA,uBACAC;MACA;QACA;QACA;MACA;MACA;QACAF;QACAG;QACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvRA;AAAA;AAAA;AAAA;AAA04B,CAAgB,o2BAAG,EAAC,C;;;;;;;;;;;ACA95B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA,wCAAsT,CAAgB,uXAAG,EAAC,C;;;;;;;;;;;;ACA1U;AAAe;AACf;AACA;AACA;;AAEA,M", "file": "components/jyf-parser/libs/trees.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./trees.vue?vue&type=template&id=13da2543&filter-modules=eyJoYW5kbGVyIjp7InR5cGUiOiJzY3JpcHQiLCJjb250ZW50IjoiIiwic3RhcnQiOjUxNDgsImF0dHJzIjp7Im1vZHVsZSI6ImhhbmRsZXIiLCJsYW5nIjoid3hzIiwic3JjIjoiLi9oYW5kbGVyLnd4cyJ9LCJlbmQiOjUxNDh9fQ%3D%3D&\"\nvar renderjs\nimport script from \"./trees.vue?vue&type=script&lang=js&\"\nexport * from \"./trees.vue?vue&type=script&lang=js&\"\nimport style0 from \"./trees.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\n/* custom blocks */\nimport block0 from \"./handler.wxs?vue&type=custom&index=0&blockType=script&issuerPath=D%3A%5Cwork%5Ckecheng_v3%5Ccomponents%5Cjyf-parser%5Clibs%5Ctrees.vue&module=handler&lang=wxs\"\nif (typeof block0 === 'function') block0(component)\n\ncomponent.options.__file = \"components/jyf-parser/libs/trees.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./trees.vue?vue&type=template&id=13da2543&filter-modules=eyJoYW5kbGVyIjp7InR5cGUiOiJzY3JpcHQiLCJjb250ZW50IjoiIiwic3RhcnQiOjUxNDgsImF0dHJzIjp7Im1vZHVsZSI6ImhhbmRsZXIiLCJsYW5nIjoid3hzIiwic3JjIjoiLi9oYW5kbGVyLnd4cyJ9LCJlbmQiOjUxNDh9fQ%3D%3D&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./trees.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./trees.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :class=\"'interlayer '+(c||'')\" :style=\"s\">\r\n\t\t<block v-for=\"(n, i) in nodes\" v-bind:key=\"i\">\r\n\t\t\t<!--图片-->\r\n\t\t\t<view v-if=\"n.name=='img'\" :class=\"'_img '+n.attrs.class\" :style=\"n.attrs.style\" :data-attrs=\"n.attrs\" @tap=\"imgtap\">\r\n\t\t\t\t<rich-text v-if=\"ctrl[i]!=0\" :nodes=\"[{attrs:{src:loading&&(ctrl[i]||0)<2?loading:(lazyLoad&&!ctrl[i]?placeholder:(ctrl[i]==3?errorImg:n.attrs.src||'')),alt:n.attrs.alt||'',width:n.attrs.width||'',style:'-webkit-touch-callout:none;max-width:100%;display:block'+(n.attrs.height?';height:'+n.attrs.height:'')},name:'img'}]\" />\r\n\t\t\t\t<image class=\"_image\" :src=\"lazyLoad&&!ctrl[i]?placeholder:n.attrs.src\" :lazy-load=\"lazyLoad\"\r\n\t\t\t\t :show-menu-by-longpress=\"!n.attrs.ignore\" :data-i=\"i\" :data-index=\"n.attrs.i\" data-source=\"img\" @load=\"loadImg\"\r\n\t\t\t\t @error=\"error\" />\r\n\t\t\t</view>\r\n\t\t\t<!--文本-->\r\n\t\t\t<text v-else-if=\"n.type=='text'\" decode>{{n.text}}</text>\r\n\t\t\t<!--#ifndef MP-BAIDU-->\r\n\t\t\t<text v-else-if=\"n.name=='br'\">\\n</text>\r\n\t\t\t<!--#endif-->\r\n\t\t\t<!--视频-->\r\n\t\t\t<view v-else-if=\"((n.lazyLoad&&!n.attrs.autoplay)||(n.name=='video'&&!loadVideo))&&ctrl[i]==undefined\" :id=\"n.attrs.id\" :class=\"'_video '+(n.attrs.class||'')\"\r\n\t\t\t :style=\"n.attrs.style\" :data-i=\"i\" @tap=\"_loadVideo\" />\r\n\t\t\t<video v-else-if=\"n.name=='video'\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :autoplay=\"n.attrs.autoplay||ctrl[i]==0\"\r\n\t\t\t :controls=\"n.attrs.controls\" :loop=\"n.attrs.loop\" :muted=\"n.attrs.muted\" :poster=\"n.attrs.poster\" :src=\"n.attrs.source[ctrl[i]||0]\"\r\n\t\t\t :unit-id=\"n.attrs['unit-id']\" :data-id=\"n.attrs.id\" :data-i=\"i\" data-source=\"video\" @error=\"error\" @play=\"play\" />\r\n\t\t\t<!--音频-->\r\n\t\t\t<audio v-else-if=\"n.name=='audio'\" :ref=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :author=\"n.attrs.author\"\r\n\t\t\t :autoplay=\"n.attrs.autoplay\" :controls=\"n.attrs.controls\" :loop=\"n.attrs.loop\" :name=\"n.attrs.name\" :poster=\"n.attrs.poster\"\r\n\t\t\t :src=\"n.attrs.source[ctrl[i]||0]\" :data-i=\"i\" :data-id=\"n.attrs.id\" data-source=\"audio\"\r\n\t\t\t @error.native=\"error\" @play.native=\"play\" />\r\n\t\t\t<!--链接-->\r\n\t\t\t<view v-else-if=\"n.name=='a'\" :id=\"n.attrs.id\" :class=\"'_a '+(n.attrs.class||'')\" hover-class=\"_hover\" :style=\"n.attrs.style\"\r\n\t\t\t :data-attrs=\"n.attrs\" @tap=\"linkpress\">\r\n\t\t\t\t<trees class=\"_span\" c=\"_span\" :nodes=\"n.children\" />\r\n\t\t\t</view>\r\n\t\t\t<!--广告-->\r\n\t\t\t<!--<ad v-else-if=\"n.name=='ad'\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :unit-id=\"n.attrs['unit-id']\" :appid=\"n.attrs.appid\" :apid=\"n.attrs.apid\" :type=\"n.attrs.type\" :adpid=\"n.attrs.adpid\" data-source=\"ad\" @error=\"error\" />-->\r\n\t\t\t<!--列表-->\r\n\t\t\t<view v-else-if=\"n.name=='li'\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"(n.attrs.style||'')+';display:flex;flex-direction:row'\">\r\n\t\t\t\t<view v-if=\"n.type=='ol'\" class=\"_ol-bef\">{{n.num}}</view>\r\n\t\t\t\t<view v-else class=\"_ul-bef\">\r\n\t\t\t\t\t<view v-if=\"n.floor%3==0\" class=\"_ul-p1\">█</view>\r\n\t\t\t\t\t<view v-else-if=\"n.floor%3==2\" class=\"_ul-p2\" />\r\n\t\t\t\t\t<view v-else class=\"_ul-p1\" style=\"border-radius:50%\">█</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<trees class=\"_li\" c=\"_li\" :nodes=\"n.children\" :lazyLoad=\"lazyLoad\" :loading=\"loading\" />\r\n\t\t\t</view>\r\n\t\t\t<!--表格-->\r\n\t\t\t<view v-else-if=\"n.name=='table'&&n.c\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"(n.attrs.style||'')+';display:table'\">\r\n\t\t\t\t<view v-for=\"(tbody, o) in n.children\" v-bind:key=\"o\" :class=\"tbody.attrs.class\" :style=\"(tbody.attrs.style||'')+(tbody.name[0]=='t'?';display:table-'+(tbody.name=='tr'?'row':'row-group'):'')\">\r\n\t\t\t\t\t<view v-for=\"(tr, p) in tbody.children\" v-bind:key=\"p\" :class=\"tr.attrs.class\" :style=\"(tr.attrs.style||'')+(tr.name[0]=='t'?';display:table-'+(tr.name=='tr'?'row':'cell'):'')\">\r\n\t\t\t\t\t\t<trees v-if=\"tr.name=='td'\" :nodes=\"tr.children\" />\r\n\t\t\t\t\t\t<trees v-else v-for=\"(td, q) in tr.children\" v-bind:key=\"q\" :class=\"td.attrs.class\" :c=\"td.attrs.class\" :style=\"(td.attrs.style||'')+(td.name[0]=='t'?';display:table-'+(td.name=='tr'?'row':'cell'):'')\"\r\n\t\t\t\t\t\t :s=\"(td.attrs.style||'')+(td.name[0]=='t'?';display:table-'+(td.name=='tr'?'row':'cell'):'')\" :nodes=\"td.children\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!--#ifdef APP-PLUS-->\r\n\t\t\t<iframe v-else-if=\"n.name=='iframe'\" :style=\"n.attrs.style\" :allowfullscreen=\"n.attrs.allowfullscreen\" :frameborder=\"n.attrs.frameborder\"\r\n\t\t\t :width=\"n.attrs.width\" :height=\"n.attrs.height\" :src=\"n.attrs.src\" />\r\n\t\t\t<embed v-else-if=\"n.name=='embed'\" :style=\"n.attrs.style\" :width=\"n.attrs.width\" :height=\"n.attrs.height\" :src=\"n.attrs.src\" />\r\n\t\t\t<!--#endif-->\r\n\t\t\t<!--富文本-->\r\n\t\t\t<!--#ifdef MP-WEIXIN || MP-QQ || APP-PLUS-->\r\n\t\t\t<rich-text v-else-if=\"handler.use(n)\" :id=\"n.attrs.id\" :class=\"'_p __'+n.name\" :nodes=\"[n]\" />\r\n\t\t\t<!--#endif-->\r\n\t\t\t<!--#ifndef MP-WEIXIN || MP-QQ || APP-PLUS-->\r\n\t\t\t<rich-text v-else-if=\"!n.c\" :id=\"n.attrs.id\" :nodes=\"[n]\" style=\"display:inline\" />\r\n\t\t\t<!--#endif-->\r\n\t\t\t<trees v-else :class=\"(n.attrs.id||'')+' _'+n.name+' '+(n.attrs.class||'')\" :c=\"(n.attrs.id||'')+' _'+n.name+' '+(n.attrs.class||'')\"\r\n\t\t\t :style=\"n.attrs.style\" :s=\"n.attrs.style\" :nodes=\"n.children\" :lazyLoad=\"lazyLoad\" :loading=\"loading\" />\r\n\t\t</block>\r\n\t</view>\r\n</template>\r\n<script module=\"handler\" lang=\"wxs\" src=\"./handler.wxs\"></script>\r\n<script>\r\n\tglobal.Parser = {};\r\n\timport trees from './trees'\r\n\tconst errorImg = require('../libs/config.js').errorImg;\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\ttrees\r\n\t\t},\r\n\t\tname: 'trees',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tctrl: [],\r\n\t\t\t\tplaceholder: 'data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"300\" height=\"225\"/>',\r\n\t\t\t\terrorImg,\r\n\t\t\t\tloadVideo: typeof plus == 'undefined',\r\n\t\t\t\t// #ifndef MP-ALIPAY\r\n\t\t\t\tc: '',\r\n\t\t\t\ts: ''\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tnodes: Array,\r\n\t\t\tlazyLoad: Boolean,\r\n\t\t\tloading: String,\r\n\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\tc: String,\r\n\t\t\ts: String\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tfor (this.top = this.$parent; this.top.$options.name != 'parser'; this.top = this.top.$parent);\r\n\t\t\tthis.init();\r\n\t\t},\r\n\t\t// #ifdef APP-PLUS\r\n\t\tbeforeDestroy() {\r\n\t\t\tthis.observer && this.observer.disconnect();\r\n\t\t},\r\n\t\t// #endif\r\n\t\tmethods: {\r\n\t\t\tinit() {\r\n\t\t\t\tfor (var i = this.nodes.length, n; n = this.nodes[--i];) {\r\n\t\t\t\t\tif (n.name == 'img') {\r\n\t\t\t\t\t\tthis.top.imgList.setItem(n.attrs.i, n.attrs['original-src'] || n.attrs.src);\r\n\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\tif (this.lazyLoad && !this.observer) {\r\n\t\t\t\t\t\t\tthis.observer = uni.createIntersectionObserver(this).relativeToViewport({\r\n\t\t\t\t\t\t\t\ttop: 500,\r\n\t\t\t\t\t\t\t\tbottom: 500\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tthis.observer.observe('._img', res => {\r\n\t\t\t\t\t\t\t\t\tif (res.intersectionRatio) {\r\n\t\t\t\t\t\t\t\t\t\tfor (var j = this.nodes.length; j--;)\r\n\t\t\t\t\t\t\t\t\t\t\tif (this.nodes[j].name == 'img')\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.$set(this.ctrl, j, 1);\r\n\t\t\t\t\t\t\t\t\t\tthis.observer.disconnect();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}, 0)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t} else if (n.name == 'video' || n.name == 'audio') {\r\n\t\t\t\t\t\tvar ctx;\r\n\t\t\t\t\t\tif (n.name == 'video') {\r\n\t\t\t\t\t\t\tctx = uni.createVideoContext(n.attrs.id\r\n\t\t\t\t\t\t\t\t// #ifndef MP-BAIDU\r\n\t\t\t\t\t\t\t\t, this\r\n\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t} else if (this.$refs[n.attrs.id])\r\n\t\t\t\t\t\t\tctx = this.$refs[n.attrs.id][0];\r\n\t\t\t\t\t\tif (ctx) {\r\n\t\t\t\t\t\t\tctx.id = n.attrs.id;\r\n\t\t\t\t\t\t\tthis.top.videoContexts.push(ctx);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t// APP 上避免 video 错位需要延时渲染\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.loadVideo = true;\r\n\t\t\t\t}, 1000)\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tplay(e) {\r\n\t\t\t\tvar contexts = this.top.videoContexts;\r\n\t\t\t\tif (contexts.length > 1 && this.top.autopause)\r\n\t\t\t\t\tfor (var i = contexts.length; i--;)\r\n\t\t\t\t\t\tif (contexts[i].id != e.currentTarget.dataset.id)\r\n\t\t\t\t\t\t\tcontexts[i].pause();\r\n\t\t\t},\r\n\t\t\timgtap(e) {\r\n\t\t\t\tvar attrs = e.currentTarget.dataset.attrs;\r\n\t\t\t\tif (!attrs.ignore) {\r\n\t\t\t\t\tvar preview = true,\r\n\t\t\t\t\t\tdata = {\r\n\t\t\t\t\t\t\tid: e.target.id,\r\n\t\t\t\t\t\t\tsrc: attrs.src,\r\n\t\t\t\t\t\t\tignore: () => preview = false\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\tglobal.Parser.onImgtap && global.Parser.onImgtap(data);\r\n\t\t\t\t\tthis.top.$emit('imgtap', data);\r\n\t\t\t\t\tif (preview) {\r\n\t\t\t\t\t\tvar urls = this.top.imgList,\r\n\t\t\t\t\t\t\tcurrent = urls[attrs.i] ? parseInt(attrs.i) : (urls = [attrs.src], 0);\r\n\t\t\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t\t\tcurrent,\r\n\t\t\t\t\t\t\turls\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tloadImg(e) {\r\n\t\t\t\tvar i = e.currentTarget.dataset.i;\r\n\t\t\t\tif (this.lazyLoad && !this.ctrl[i]) {\r\n\t\t\t\t\t// #ifdef QUICKAPP-WEBVIEW\r\n\t\t\t\t\tthis.$set(this.ctrl, i, 0);\r\n\t\t\t\t\tthis.$nextTick(function() {\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\t\t\tthis.$set(this.ctrl, i, 1);\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t// #ifdef QUICKAPP-WEBVIEW\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t} else if (this.loading && this.ctrl[i] != 2) {\r\n\t\t\t\t\t// #ifdef QUICKAPP-WEBVIEW\r\n\t\t\t\t\tthis.$set(this.ctrl, i, 0);\r\n\t\t\t\t\tthis.$nextTick(function() {\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\tthis.$set(this.ctrl, i, 2);\r\n\t\t\t\t\t\t// #ifdef QUICKAPP-WEBVIEW\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tlinkpress(e) {\r\n\t\t\t\tvar jump = true,\r\n\t\t\t\t\tattrs = e.currentTarget.dataset.attrs;\r\n\t\t\t\tattrs.ignore = () => jump = false;\r\n\t\t\t\tglobal.Parser.onLinkpress && global.Parser.onLinkpress(attrs);\r\n\t\t\t\tthis.top.$emit('linkpress', attrs);\r\n\t\t\t\tif (jump) {\r\n\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\tif (attrs['app-id']) {\r\n\t\t\t\t\t\treturn uni.navigateToMiniProgram({\r\n\t\t\t\t\t\t\tappId: attrs['app-id'],\r\n\t\t\t\t\t\t\tpath: attrs.path\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tif (attrs.href) {\r\n\t\t\t\t\t\tif (attrs.href[0] == '#') {\r\n\t\t\t\t\t\t\tif (this.top.useAnchor)\r\n\t\t\t\t\t\t\t\tthis.top.navigateTo({\r\n\t\t\t\t\t\t\t\t\tid: attrs.href.substring(1)\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else if (attrs.href.indexOf('http') == 0 || attrs.href.indexOf('//') == 0) {\r\n\t\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\t\tplus.runtime.openWeb(attrs.href);\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\t\t\tdata: attrs.href,\r\n\t\t\t\t\t\t\t\tsuccess: () =>\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '链接已复制'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t} else\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: attrs.href,\r\n\t\t\t\t\t\t\t\tfail() {\r\n\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\turl: attrs.href,\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\terror(e) {\r\n\t\t\t\tvar target = e.currentTarget,\r\n\t\t\t\t\tsource = target.dataset.source,\r\n\t\t\t\t\ti = target.dataset.i;\r\n\t\t\t\tif (source == 'video' || source == 'audio') {\r\n\t\t\t\t\t// 加载其他 source\r\n\t\t\t\t\tvar index = this.ctrl[i] ? this.ctrl[i].i + 1 : 1;\r\n\t\t\t\t\tif (index < this.nodes[i].attrs.source.length)\r\n\t\t\t\t\t\tthis.$set(this.ctrl, i, index);\r\n\t\t\t\t\tif (e.detail.__args__)\r\n\t\t\t\t\t\te.detail = e.detail.__args__[0];\r\n\t\t\t\t} else if (errorImg && source == 'img') {\r\n\t\t\t\t\tthis.top.imgList.setItem(target.dataset.index, errorImg);\r\n\t\t\t\t\tthis.$set(this.ctrl, i, 3);\r\n\t\t\t\t}\r\n\t\t\t\tthis.top && this.top.$emit('error', {\r\n\t\t\t\t\tsource,\r\n\t\t\t\t\ttarget,\r\n\t\t\t\t\terrMsg: e.detail.errMsg\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t_loadVideo(e) {\r\n\t\t\t\tthis.$set(this.ctrl, e.target.dataset.i, 0);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 在这里引入自定义样式 */\r\n\r\n\t/* 链接和图片效果 */\r\n\t._a {\r\n\t\tdisplay: inline;\r\n\t\tpadding: 1.5px 0 1.5px 0;\r\n\t\tcolor: #366092;\r\n\t\tword-break: break-all;\r\n\t}\r\n\r\n\t._hover {\r\n\t\ttext-decoration: underline;\r\n\t\topacity: 0.7;\r\n\t}\r\n\r\n\t._img {\r\n\t\tdisplay: inline-block;\r\n\t\tmax-width: 100%;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t/* #ifdef MP-WEIXIN */\r\n\t:host {\r\n\t\tdisplay: inline;\r\n\t}\r\n\r\n\t/* #endif */\r\n\r\n\t/* #ifndef MP-ALIPAY || APP-PLUS */\r\n\t.interlayer {\r\n\t\tdisplay: inherit;\r\n\t\tflex-direction: inherit;\r\n\t\tflex-wrap: inherit;\r\n\t\talign-content: inherit;\r\n\t\talign-items: inherit;\r\n\t\tjustify-content: inherit;\r\n\t\twidth: 100%;\r\n\t\twhite-space: inherit;\r\n\t}\r\n\r\n\t/* #endif */\r\n\r\n\t._b,\r\n\t._strong {\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* #ifndef MP-ALIPAY */\r\n\t._blockquote,\r\n\t._div,\r\n\t._p,\r\n\t._ol,\r\n\t._ul,\r\n\t._li {\r\n\t\tdisplay: block;\r\n\t}\r\n\t\r\n\t/* #endif */\r\n\r\n\t._code {\r\n\t\tfont-family: monospace;\r\n\t}\r\n\r\n\t._del {\r\n\t\ttext-decoration: line-through;\r\n\t}\r\n\r\n\t._em,\r\n\t._i {\r\n\t\tfont-style: italic;\r\n\t}\r\n\r\n\t._h1 {\r\n\t\tfont-size: 2em;\r\n\t}\r\n\r\n\t._h2 {\r\n\t\tfont-size: 1.5em;\r\n\t}\r\n\r\n\t._h3 {\r\n\t\tfont-size: 1.17em;\r\n\t}\r\n\r\n\t._h5 {\r\n\t\tfont-size: 0.83em;\r\n\t}\r\n\r\n\t._h6 {\r\n\t\tfont-size: 0.67em;\r\n\t}\r\n\r\n\t._h1,\r\n\t._h2,\r\n\t._h3,\r\n\t._h4,\r\n\t._h5,\r\n\t._h6 {\r\n\t\tdisplay: block;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t._image {\r\n\t\tdisplay: block;\r\n\t\twidth: 100%;\r\n\t\theight: 360px;\r\n\t\tmargin-top: -360px;\r\n\t\topacity: 0;\r\n\t}\r\n\r\n\t._ins {\r\n\t\ttext-decoration: underline;\r\n\t}\r\n\r\n\t._li {\r\n\t\tflex: 1;\r\n\t\twidth: 0;\r\n\t}\r\n\r\n\t._ol-bef {\r\n\t\twidth: 36px;\r\n\t\tmargin-right: 5px;\r\n\t\ttext-align: right;\r\n\t}\r\n\r\n\t._ul-bef {\r\n\t\tdisplay: block;\r\n\t\tmargin: 0 12px 0 23px;\r\n\t\tline-height: normal;\r\n\t}\r\n\r\n\t._ol-bef,\r\n\t._ul-bef {\r\n\t\tflex: none;\r\n\t\tuser-select: none;\r\n\t}\r\n\r\n\t._ul-p1 {\r\n\t\tdisplay: inline-block;\r\n\t\twidth: 0.3em;\r\n\t\theight: 0.3em;\r\n\t\toverflow: hidden;\r\n\t\tline-height: 0.3em;\r\n\t}\r\n\r\n\t._ul-p2 {\r\n\t\tdisplay: inline-block;\r\n\t\twidth: 0.23em;\r\n\t\theight: 0.23em;\r\n\t\tborder: 0.05em solid black;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t._q::before {\r\n\t\tcontent: '\"';\r\n\t}\r\n\r\n\t._q::after {\r\n\t\tcontent: '\"';\r\n\t}\r\n\r\n\t._sub {\r\n\t\tfont-size: smaller;\r\n\t\tvertical-align: sub;\r\n\t}\r\n\r\n\t._sup {\r\n\t\tfont-size: smaller;\r\n\t\tvertical-align: super;\r\n\t}\r\n\r\n\t/* #ifdef MP-ALIPAY || APP-PLUS || QUICKAPP-WEBVIEW */\r\n\t._abbr,\r\n\t._b,\r\n\t._code,\r\n\t._del,\r\n\t._em,\r\n\t._i,\r\n\t._ins,\r\n\t._label,\r\n\t._q,\r\n\t._span,\r\n\t._strong,\r\n\t._sub,\r\n\t._sup {\r\n\t\tdisplay: inline;\r\n\t}\r\n\r\n\t/* #endif */\r\n\r\n\t/* #ifdef MP-WEIXIN || MP-QQ */\r\n\t.__bdo,\r\n\t.__bdi,\r\n\t.__ruby,\r\n\t.__rt {\r\n\t\tdisplay: inline-block;\r\n\t}\r\n\r\n\t/* #endif */\r\n\t._video {\r\n\t\tposition: relative;\r\n\t\tdisplay: inline-block;\r\n\t\twidth: 300px;\r\n\t\theight: 225px;\r\n\t\tbackground-color: black;\r\n\t}\r\n\r\n\t._video::after {\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\tmargin: -15px 0 0 -15px;\r\n\t\tcontent: '';\r\n\t\tborder-color: transparent transparent transparent white;\r\n\t\tborder-style: solid;\r\n\t\tborder-width: 15px 0 15px 30px;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./trees.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./trees.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1735872263542\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!./handler.wxs?vue&type=custom&index=0&blockType=script&issuerPath=D%3A%5Cwork%5Ckecheng_v3%5Ccomponents%5Cjyf-parser%5Clibs%5Ctrees.vue&module=handler&lang=wxs\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!./handler.wxs?vue&type=custom&index=0&blockType=script&issuerPath=D%3A%5Cwork%5Ckecheng_v3%5Ccomponents%5Cjyf-parser%5Clibs%5Ctrees.vue&module=handler&lang=wxs\"", "export default function (Component) {\n       if(!Component.options.wxsCallMethods){\n         Component.options.wxsCallMethods = []\n       }\n       \n     }"], "sourceRoot": ""}