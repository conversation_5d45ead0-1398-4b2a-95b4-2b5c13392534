page{
    background: #F5F7FB;
    color: #333;
    font-family: 'PingFang SC';
}
.live-header{
    width: 100%;
    height: 164upx;
    background-image: url('@/static/image <EMAIL>');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    padding: 20upx 30upx;
    .search-box-left{
        display: flex;
        align-items: center;
        padding: 8upx 10upx;
        width: 89%;
        height: 100%;
        background-color: #fff;
        border-radius: 40upx;
        input{
            width: 100%;
            height: 100%;
            border: none;
            outline: none;
            background-color: transparent;
            line-height: 44upx;
        }
    }
    .search-box-right{
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 90%;
        margin-top: 40upx;
        .search-box-right-item{
            display: flex;
            align-items: center;
         
            height: 100%;
            image{
                width: 44upx;
                height: 44upx;
            }
            text{
                font-size: 34upx;
                color: #333;
                font-weight: bold;
                margin-left: 10upx;
            }
        }
        .search-box-right-item2{
            display: flex;
            align-items: center;
       
        
            text{
                font-size: 28upx;
                color: #999999;
            }
        }
    }
}

.box {
  padding: 10upx 30upx;
  position: relative;

  
  .box-item {
    margin-bottom: 40upx;
    position: relative;
      
  &:before {
    content: '';
    position: absolute;
    left: 8upx;
    top: 52upx;
    bottom: 0;
    height: 80%;
    width: 2upx;
    background-color: #E5E5E5;
    z-index: 0;
  }
    .box-item-time {
      display: flex;
      align-items: center;
      margin-bottom: 16upx;
      position: relative;
      z-index: 1;
      
      .box-item-doc {
        width: 16upx;
        height: 16upx;
        border-radius: 50%;
        background-color: #0078FF;
        margin-right: 10upx;
        box-shadow: 0 0 0 6upx rgba(0, 120, 255, 0.2);
      }
      
      text {
        font-size: 29upx;
        padding-left: 30upx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #333333;
      }
    }
    
    .cond {
      display: flex;
      margin-left: 22upx;
      width: 92%;
      transform: translateX(30upx);
      .cond-left {
        width: 6upx;
        border-radius: 3upx 0 0 3upx;
      }
      
      .cond-item {
        flex: 1;
        background-color: #FFFFFF;
        border-radius:20upx;
        padding: 20upx;
        box-shadow: 0 2upx 12upx rgba(0, 0, 0, 0.08);
        
        .cond-item-top {
          display: flex;
          margin-bottom: 20upx;
          
          image {
            width: 240upx;
            height: 120upx;
            border-radius: 8upx;
            margin-right: 20upx;
            border: 1upx solid #EEEEEE;
          }
          
          .cond-itemtti {
            flex: 1;
            font-size: 28upx;
            font-weight: bold;
            line-height: 1.4;
            display: flex;
           
          }
        }
        
        .cond-item-bottom {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .condsale {
            display: flex;
            align-items: center;
            
            text {
              font-size: 20upx;
              color: #2D9AFE;
              background: rgba(45,154,254,0.1);
              padding: 4upx 6upx;
              border-radius: 4upx;
              margin-right: 20upx;
            }
            
            .cond-item-right2 {
              font-size: 20upx;
              color: #999;
              
              text {
                color: #FF5722;
                background-color: transparent;
                padding: 0;
                margin: 0 2upx;
              }
            }
          }
          
          .cond-item-right {
            text {
              display: inline-block;
              background: #2D9AFE;
              border-radius: 39px 39px 39px 39px;
              color: #FFFFFF;
              font-size: 23upx;
              padding: 10upx 30upx;
              border-radius: 30upx;
            }
          }
        }
      }
    }
  }
}

.lubright {
    display: flex;
    align-items: center;
    padding: 20upx 30upx;
    .lubright-item {
        display: flex;
        align-items: center;
        
        image {
            width: 44upx;
            height: 44upx;
        }
        
        text {
            font-size: 32upx;
            color: #333;
            font-weight: bold;
            margin-left: 10upx;
        }
    }
}

.lublist {
  margin-top: 0upx;
  padding: 0;
 
  .lublist-item {
    display: flex;
    align-items: center;
    padding: 24upx 30upx;
    border-bottom: 1upx solid #F5F5F5;
    
    &:last-child {
      border-bottom: none;
    }
    
    image {
      width: 74upx;
      height: 74upx;
      transform: translateY(-20upx);
      margin-right: 20upx;
      flex-shrink: 0;
      opacity: 0.6;
    }
    
    .lublist-item-text {
      flex: 1;
      border-bottom: 1upx solid #e7e7e7;
      padding-bottom: 14upx;
      .lublist-item-text-top {
        font-size: 28upx;
        color: #333;
        font-weight: bold;
        margin-bottom: 10upx;
        line-height: 1.4;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .lublist-item-text-bottom {
        font-size: 24upx;
        color: #999;
        display: flex;
        align-items: center;
        padding: 10upx 0upx;
        text{
            padding: 0 10upx;
            display: inline-block;
            width: 30upx;
            display: flex;
            align-items: center;
            justify-content: center;
        }
      }
    }
  }
}

.reservation-popup {
  padding: 30upx;
  max-height: 80vh;

  .reservation-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20upx;
    border-bottom: 1px solid #eee;
    margin-bottom: 20upx;

    text {
      font-size: 32upx;
      font-weight: bold;
      color: #333;
    }
  }

  .reservation-list {
    max-height: 60vh;
  }

  .reservation-item {
    display: flex;
    padding: 20upx 0;
    border-bottom: 1px solid #f5f5f5;

    .reservation-img {
      width: 120upx;
      height: 120upx;
      border-radius: 10upx;
      margin-right: 20upx;
      flex-shrink: 0;
    }

    .reservation-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      
      .reservation-title-text {
        font-size: 28upx;
        color: #333;
        font-weight: 500;
        margin-bottom: 10upx;
        line-height: 1.4;
      }
      
      .reservation-time {
        font-size: 24upx;
        color: #666;
        margin-bottom: 10upx;
      }
      
      .reservation-status {
        text {
          display: inline-block;
          font-size: 22upx;
          padding: 6upx 16upx;
          border-radius: 30upx;
          
          &.status-live {
            background-color: #ff5722;
            color: #fff;
          }
          
          &.status-reserved {
            background-color: #2D9AFE;
            color: #fff;
          }
        }
      }
    }
  }
  
  .no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200upx;
    
    text {
      font-size: 28upx;
      color: #999;
    }
  }
}