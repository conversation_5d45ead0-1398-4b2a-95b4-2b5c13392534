(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/baoming/baoming"],{"2eba":function(t,n,a){"use strict";a.r(n);var e=a("a422"),o=a("a029");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(n,t,(function(){return o[t]}))}(i);a("8005");var c=a("828b"),s=Object(c["a"])(o["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=s.exports},5445:function(t,n,a){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e={components:{Footer:function(){a.e("components/footer").then(function(){return resolve(a("ccb8"))}.bind(null,a)).catch(a.oe)}},data:function(){return{blx:"",baocats:[],nodata:"",page:1,baolist:[]}},onLoad:function(){var t=this;this.$api.get("baoming/cats").then((function(n){t.baocats=n})),this.setbCat("")},onReachBottom:function(){""==this.nodata&&(this.page+=1,this.$api.msg("数据读取中..."),this.setbCat(this.blx))},methods:{tonbap:function(t){location.href=t},clickCat:function(t){this.blx=t,this.page=1,this.baolist=[],this.setbCat(t)},setbCat:function(t){var n=this;this.blx=t,this.$api.get("baoming/getList",{lx:t,page:this.page}).then((function(t){t&&0!=t.length?n.baolist=1==n.page?t:n.baolist.concat(t):n.nodata="暂无更多数据"}))}}};n.default=e},a029:function(t,n,a){"use strict";a.r(n);var e=a("5445"),o=a.n(e);for(var i in e)["default"].indexOf(i)<0&&function(t){a.d(n,t,(function(){return e[t]}))}(i);n["default"]=o.a},a422:function(t,n,a){"use strict";a.d(n,"b",(function(){return e})),a.d(n,"c",(function(){return o})),a.d(n,"a",(function(){}));var e=function(){var t=this.$createElement;this._self._c},o=[]},f738:function(t,n,a){"use strict";(function(t,n){var e=a("47a9");a("c7a0");e(a("3240"));var o=e(a("2eba"));t.__webpack_require_UNI_MP_PLUGIN__=a,n(o.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])}},[["f738","common/runtime","common/vendor"]]]);