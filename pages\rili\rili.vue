<template>
	<view>
		<Rili @childEvent="handleChildEvent"></Rili>
		<view class="bpox" style="display: none;">
			<!--
			<view class="stt1">
				<image src="/static/<EMAIL>"></image>
				<text style="font-size: 32upx;">{{day || "今日"}}考试</text>
			</view>
			<view class="item" v-for="(c,k) in kaoshi">
				<view class="sttt">{{c.bt}}</view>
				<view class="cont">
					{{c.nr}}
				</view>
				
			</view>
		-->

		</view>
		<view class="bpoxnew" v-if="kaoshi && kaoshi.length > 0">
			<view class="li" v-for="(c,k) in kaoshi" @click="todurl(c)">
				<view class="h1">
					<view class="h11"></view>
					<text>{{c.bt}}</text>
				</view>
				<view class="h2">
					<view class="sleft">
						<image src="/static/15.png"></image>
						<text>{{c.optdt.replace('-','.')}}</text>
					</view>
					<view class="sright">
						<text>查看详情</text>
						<u-icon name="arrow-down" color="#666666" size="12"></u-icon>
					</view>
				</view>
			</view>
			
		</view>
		<view class="none" v-if="!kaoshi || (kaoshi.length == 0)">
			暂无记录
			<!-- <u-empty mode="data" text='暂无记录' icon="http://cdn.uviewui.com/uview/empty/data.png"></u-empty> -->
		</view>
		<view class="cop"></view>
		<view class="cop"></view>
		
		<Footer :act="'rili'"></Footer>
	</view>
</template>

<script>
	import Footer from "@/components/footer.vue";
	import Rili from "@/components/d-rili/d-rili.vue";
	export default {
		components: {
			Footer,
			Rili
		},
		data() {
			return {
				kaoshi: [],
				day: "",
			}
		},
		onLoad() {
			this.$api.get('rili/getDays', {
				'day': ''
			}).then(res => {
				this.kaoshi = res;
			})
			this.$api.get('user/getUserApiUrl',{uid:uni.getStorageSync("uid"),'redirect':3}).then(res => {
				if(res){
					uni.setStorageSync('apikcurl' , res);
				}
			})	
		},
		methods: {
			todurl(c){
				if (c.ljdz == '') {
					this.$api.tourl('/pages/rili/detail?id=' + c.id);
				} else {
					location.href = c.ljdz;
				}
			},
			handleChildEvent(data) {
				this.day = data;
				this.$api.get('rili/getDays', {
					'day': data
				}).then(res => {
					this.kaoshi = res;
				})
			}
		}
	}
</script>

<style lang="scss">
   .bpoxnew{
	width: 100%;
	background: linear-gradient( 178deg, #F5F7FB 0%, #F5F7FB 45%, #FFFFFF 52%);
	overflow: hidden;
	padding: 10upx 32upx 32upx 32upx;
	box-sizing: border-box;
	.li{
		width: 90%;
		height: 108upx;
		background: linear-gradient( 180deg, #E8F5FF 0%, #FFFFFF 100%);
		
		box-shadow: 0px 2px 6px 0px rgba(0,0,0,0.08);
		border-radius: 16upx;
		border: 1px solid #FFFFFF;
		padding: 32upx 32upx 32upx 32upx;
		margin-bottom: 24upx;
		.h1{
			display: flex;
			align-items: center;
			.h11{
				width: 8upx;
				height: 26upx;
				background: linear-gradient( 180deg, #6EA2FF 0%, #2695FF 100%);
				border-radius: 54upx;
			}
			text{
				font-size: 32upx;
				color: #222222;
				font-weight: bold;
				padding-left: 16upx;
			}
		}
		.h2{
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 24upx;
			.sleft{
				display: flex;
				align-items: center;
				image{
					width: 28upx;
					height: 28upx;
				}
				text{
					font-size: 28upx;
					color: #666666;
					padding-left: 16upx;
				}
			}
			.sright{
				display: flex;
				align-items: center;
				text{
					font-size: 28upx;
					color: #666666 ;
					padding-right: 16upx;
				}
				transform: translateX(-20upx);
			}
		}
	}
   }
	page {
		font-family: 'PingFang';
		background-color: #fff;
	}
	
	.bpox {
		font-family: 'PingFang';
		width: 100%;
		background-color: #ffffff;
		overflow: hidden;
		padding: 32upx;
		box-sizing: border-box;

		.stt1 {
			font-family: 'PingFang';
			font-weight: bold;
			font-size: 32rpx;
			color: #222222;
			line-height: 33rpx;
			display: flex;
			align-items: center;

			image {
				width: 10rpx;
				height: 30rpx;
			}

			text {
				// font-size: ;
				// display: inline-block;
				padding-left: 20rpx;
			}
		}

		.item {
			margin: 20upx 0upx;

			.sttt {
				font-weight: bold;
				font-size: 28rpx;
				color: #333333;
				margin-top: 20rpx;
			}

			.cont {
				font-family: 'PingFang';
				font-weight: 500;
				font-size: 28rpx;
				color: #555555;
				line-height: 50rpx;
				margin-top: 20rpx;
				text-indent: 2em;
			}
		}
	}
	
	.none {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 40rpx;
		font-size: 28rpx;
		color: #c9c8d4;
	}
</style>