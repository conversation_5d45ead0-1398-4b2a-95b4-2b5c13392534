<template>
	<view>
		<view class="live-header">
			<view class="search-box">
				
				<view class="search-box-right">
					<view class="search-box-right-item">
						<image src="@/static/Frame8723.png"></image>
						<text>直播</text>
					</view>
					<view class="search-box-right-item2" @click="showMyReservations">
						<text style="transform: translateY(-4upx);">我的预约</text>
						<u-icon name="arrow-right" color="#999999" size="14"></u-icon>
					</view>
				</view>
			</view>
		</view>
		<view class="box">
			<view class="box-item" v-for="(item,index) in list1" :key="index" @click="goLive(item)">
				<view class="box-item-time">
					<view class="box-item-doc"></view>
					<text>{{item.stime}}</text>
				</view>
				<view class="cond">
					<view class="cond-left"></view>
					<view class="cond-item">
						<view class="cond-item-top">
							<image :src="item.logo"></image>
							<view class="cond-itemtti">
								{{item.title}}
							</view>
						</view>
						<view class="cond-item-bottom">
							<view class="condsale">
								<text>{{item.classname}}</text>
								<view class="cond-item-right2">
									已有<text>{{item.nums}}</text>人预约
								</view>
							</view>
							<view class="cond-item-right">
								<text>{{ item.state == 2 ? '立即预约' : '已开播' }}</text>
							</view>

						</view>
					</view>
				</view>
			</view>
			
		</view>
		<view class="lubright">
					<view class="lubright-item">
						<image src="@/static/<EMAIL>"></image>
						<text>录播</text>
					</view>
				
				</view>
		<view class="lublist">
			<view class="lublist-item" v-for="(item,index) in list2" :key="index" @click="goLive(item)">
				<image src="@/static/Group2813592.png"></image>
				<view class="lublist-item-text">
					<view class="lublist-item-text-top">
						{{item.title}}
					</view>
					<view class="lublist-item-text-bottom">
						{{item.teacher}}<text>|</text>{{item.click}}次播放
					</view>
				</view>	
			</view>
			
		</view>		
		<!-- 我的预约弹窗 -->
		<u-popup :show="showReservations" mode="center" @close="closeReservations" borderRadius="10" width="80%">
			<view class="reservation-popup">
				<view class="reservation-title">
					<text>我的预约记录</text>
					<u-icon name="close" size="20" @click="closeReservations"></u-icon>
				</view>
				<scroll-view scroll-y class="reservation-list" v-if="reservationList.length > 0">
					<view class="reservation-item" v-for="(item, index) in reservationList" :key="index" @click="goLive(item)">
						<image :src="item.logo" class="reservation-img"></image>
						<view class="reservation-info">
							<view class="reservation-title-text">{{item.title}}</view>
							<view class="reservation-time">{{item.stime}} 直播</view>
							<view class="reservation-status">
								<text :class="{'status-live': item.state==1, 'status-reserved': item.state==2}">
									{{ item.state == 2 ? '已预约' : '已开播' }}
								</text>
							</view>
						</view>
					</view>
				</scroll-view>
				<view class="no-data" v-else>
					<text>您还没有预约直播</text>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				keyword:"",
				list1:[],
				list2:[],
				showReservations: false,
				reservationList: []
			}
		},
		onLoad(){
			this.getList();
		},
		methods: {
			search(){
				this.$api.tourl('/pages/search/search');
			},
			getList(){
				this.$api.get('live/getList',{stype:1}).then(res=>{
					this.list1 = res;
				});
				this.$api.get('live/getList',{stype:2}).then(res=>{
					this.list2 = res;
				});
			},
			showMyReservations() {
				const uid = uni.getStorageSync('uid') || 0;
				
				// 显示加载框
				uni.showLoading({
					title: '加载中...'
				});
				
				// 调用API获取用户预约记录
				this.$api.get('live/getUserReservations', {uid: uid}).then(res => {
					uni.hideLoading();
					if(res.code === 1) {
						this.reservationList = res.data;
						this.showReservations = true;
					} else {
						this.$api.msg(res.msg || '获取预约记录失败');
					}
				}).catch(() => {
					uni.hideLoading();
					this.$api.msg('获取预约记录失败');
				});
			},
			closeReservations() {
				this.showReservations = false;
			},
			goLive(item){
				if(item.state == 2 && item.stype == '1'){
					this.$api.get('live/postUser',{id:item.id,uid:uni.getStorageSync('uid') || 0}).then(res=>{
						if(res.code == 1){
							this.$api.msg('预约成功');
							// 如果预约成功且预约弹窗打开，刷新预约记录
							if(this.showReservations) {
								this.showMyReservations();
							}
						}else{
							this.$api.msg("您已经预约过该直播");
						}
					});
				}else{
					location.href = item.href;
				}
			}
		}
	}
</script>

<style lang="scss" scoped src="./live.scss"></style>
