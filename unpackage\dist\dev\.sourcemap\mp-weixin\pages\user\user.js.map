{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/kecheng_v3/pages/user/user.vue?01a9", "webpack:///D:/work/kecheng_v3/pages/user/user.vue?b20f", "webpack:///D:/work/kecheng_v3/pages/user/user.vue?1eb0", "webpack:///D:/work/kecheng_v3/pages/user/user.vue?eb31", "uni-app:///pages/user/user.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Footer", "data", "user", "uid", "advs3", "advs4", "adv", "icon", "onShow", "onLoad", "methods", "clickUserAdv", "location", "tourl2", "tourl"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACe;;;AAGlE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAimB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eC2DrnB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QAAA;MAAA;MACAC;IACA;EACA;EACAC;IAAA;IACA;MAAA;IAAA;MACA;IACA;EAEA;EACAC;IAAA;IACA;MAAA;IAAA;MACA;IACA;IACA;MAAA;IAAA;MACA;IACA;IACA;MAAA;IAAA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;MACA;QACAC;MACA;IACA;IACAC;MACA;QACAD;MACA;QACAA;MACA;IAEA;IACAE;MACA;QACA;MACA;QACA;UACAF;QACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/user/user.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/user.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./user.vue?vue&type=template&id=80842834&\"\nvar renderjs\nimport script from \"./user.vue?vue&type=script&lang=js&\"\nexport * from \"./user.vue?vue&type=script&lang=js&\"\nimport style0 from \"./user.scss?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/user.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=template&id=80842834&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.$api.tourl(\"/pages/user/mbind\")\n    }\n    _vm.e1 = function ($event) {\n      return _vm.$api.tourl(\"/pages/user/editinfo\")\n    }\n    _vm.e2 = function ($event) {\n      return _vm.$api.tourl(\"/pages/msg/msg\")\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\r\n\t\t<view class=\"top\">\r\n\t\t\t<view class=\"user\">\r\n\t\t\t\t<image :src=\"user.headimgurl\" class=\"logo\"/>\r\n\t\t\t\t<view class=\"slef\">\r\n\t\t\t\t\t<view class=\"sfrit\">\r\n\t\t\t\t\t\t<text class=\"text1\">{{user.nickname}}</text>\r\n\t\t\t\t\t\t<text class=\"tag\" style=\"display: none;\">正式学员</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"sff\" @click=\"$api.tourl('/pages/user/mbind')\">\r\n\t\t\t\t\t\t<image src=\"/static/user/<EMAIL>\"></image>\r\n\t\t\t\t\t\t<text>{{!user.mobile ? '绑定手机号' : user.mobile}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav\">\r\n\t\t\t\t<view class=\"item\" v-for=\"(c,k) in advs3\" @click=\"tourl(c)\">\r\n\t\t\t\t\t<image :src=\"c.tb\"></image>\r\n\t\t\t\t\t<text>{{c.mc}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</view>\t\r\n\t\t</view>\r\n\t\t<view class=\"banner\" @click=\"clickUserAdv\">\r\n\t\t\t<image :src=\"adv[0].fmtp\"></image>\r\n\t\t</view>\r\n\t\t<view class=\"bnav\">\r\n\t\t\t<view class=\"item\" @click=\"$api.tourl('/pages/user/editinfo')\">\r\n\t\t\t\t<image :src=\"'/static/user/'+icon[0]\" class=\"slg\"></image>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<text>修改资料</text>\r\n\t\t\t\t\t<image src=\"/static/right.png\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" @click=\"$api.tourl('/pages/msg/msg')\">\r\n\t\t\t\t<image :src=\"'/static/user/'+icon[3]\" class=\"slg\"></image>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<text>我的消息</text>\r\n\t\t\t\t\t<image src=\"/static/right.png\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\"  v-for=\"(c,k) in advs4\" @click=\"tourl2(c)\">\r\n\t\t\t\t<image :src=\"c.tb\" class=\"slg\"></image>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<text>{{c.mc}}</text>\r\n\t\t\t\t\t<image src=\"/static/right.png\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\r\n\t\t</view>\r\n\t\t<view class=\"cop\"></view>\n\t\t<Footer :act=\"'user'\"></Footer>\n\t</view>\n</template>\n\n<script>\n\timport Footer from \"@/components/footer.vue\";\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tFooter\r\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tuser:{},\r\n\t\t\t\tuid:uni.getStorageSync(\"uid\"),\r\n\t\t\t\tadvs3:[],\r\n\t\t\t\tadvs4:[],\r\n\t\t\t\tadv:[{\"fmtp\":\"\"}],\r\n\t\t\t\ticon:[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"],\n\t\t\t}\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.$api.get('user/getUser',{\"uid\":this.uid}).then(res => {\r\n\t\t\t\tthis.user = res;\r\n\t\t\t});\r\n\t\t\t\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.$api.get('user/getMuint',{'type':'图标菜单'}).then(res => {\r\n\t\t\t\tthis.advs3 = res;\r\n\t\t\t})\t\r\n\t\t\tthis.$api.get('user/getMuint',{'type':'文字菜单'}).then(res => {\r\n\t\t\t\tthis.advs4 = res;\r\n\t\t\t})\r\n\t\t\tthis.$api.get('config/getAdv',{'type':3}).then(res => {\r\n\t\t\t\tthis.adv = res;\r\n\t\t\t})\t\t\r\n\t\t},\n\t\tmethods: {\r\n\t\t\tclickUserAdv(){\r\n\t\t\t\tif(this.adv[0].ljdz == ''){\r\n\t\t\t\t\tthis.$api.tourl('/pages/adv/adv?id='+this.adv[0].id);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tlocation.href = this.adv[0].ljdz;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttourl2(c){\r\n\t\t\t\tif(c.ljdz.indexOf('?') != -1){\r\n\t\t\t\t\tlocation.href = c.ljdz+'&openid='+this.user.openid;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tlocation.href = c.ljdz+'?openid='+this.user.openid;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t},\n\t\t\ttourl(c){\r\n\t\t\t\tif(c.ljdz == ''){\r\n\t\t\t\t\tthis.$api.tourl('/pages/adv/adv?id='+c.id);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif(c.ljdz.indexOf('?') != -1){\r\n\t\t\t\t\t\tlocation.href = c.ljdz+'&openid='+this.user.openid;\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tlocation.href = c.ljdz+'?openid='+this.user.openid;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style src=\"./user.scss\" lang=\"scss\"></style>\n"], "sourceRoot": ""}