<view data-event-opts="{{[['tap',[['clickHandler',['$event']]]]]}}" class="u-notice data-v-d36ba0c0" bindtap="__e"><view data-ref="u-notice__content" class="u-notice__content data-v-d36ba0c0 vue-ref" style="width:270rpx;"><view data-ref="u-notice__content__text" class="u-notice__content__text data-v-d36ba0c0 vue-ref" style="{{$root.s0}}"><block wx:for="{{innerText}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text style="font-size:30rpx;color:#000;" class="data-v-d36ba0c0">{{item}}</text></block></view></view><block wx:if="{{$root.g0}}"><view class="u-notice__right-icon data-v-d36ba0c0"><block wx:if="{{mode==='link'}}"><u-icon vue-id="32fc4566-1" name="arrow-right" size="{{17}}" color="{{color}}" class="data-v-d36ba0c0" bind:__l="__l"></u-icon></block><block wx:if="{{mode==='closable'}}"><u-icon vue-id="32fc4566-2" name="close" size="{{16}}" color="{{color}}" data-event-opts="{{[['^click',[['close']]]]}}" bind:click="__e" class="data-v-d36ba0c0" bind:__l="__l"></u-icon></block></view></block></view>