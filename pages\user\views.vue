<template>
	<view class="order">
		<block v-for="(c,k) in list">
		<view class="order_list">
			
			<view class="order_list_goods"  @click="$api.tourl('/pages/kecheng/show?id='+c.goods_id)">
				<image :src="c.img"></image>
				<view class="name">
					{{c.bt}}
				</view>
			</view>

			<view class="anniu" >
				<text>{{c.addtime}}</text>
				<text class="pay" @click="$api.tourl('/pages/kecheng/show?id='+c.goods_id+'&type=2')">继续查看</text>
				<text class="dis" @click="del(c.id)" style="border: 1px solid #ccc;">删除记录</text>
			</view>
		</view>
		<view class="cop1"></view>
		</block>
		<view class="cop">暂无更多记录</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list:[],
				
			}
		},
		onLoad() {
			this.getD();
		},
		methods: {
			getD(){
				this.$api.get('user/getUserViews',{uid:uni.getStorageSync('uid')}).then(res => {
					this.list = res;
				})
			},
			del(id){
				uni.showModal({
					title: '提示：',
					content: '您确定删除吗？',
					success: (res) => {
						if (res.confirm) {
							this.$api.get('user/delUserView',{id:id}).then(res => {
								this.getD();
							})
						} else if (res.cancel) {
							
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss">
page{
	background: #f3f5f7;
	margin: 0 auto;
	    margin-bottom: 0px;
	  color: #171920;
	  overflow-x: hidden;
	  overflow-y: scroll;
	  background: #fff;
	  position: relative;
}
.order{
	width: 100%;
	overflow: hidden;
}
.order_list{
	width: 100%;
	  overflow: hidden;
	  background: #FFF;
	  margin-bottom: 10px;
	  .stop{
		  width: 95%;
		    margin: auto;
		    height: 40px;
			padding: 20upx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.lef{
				image{
					width: 40upx;
					height: 40upx;
				}
				display: flex;
				align-items: center;
				  color: #666;
				  font-size: 24upx;
				  width: 80%;
				  text{
					  padding-left: 6upx;
				  }
			}
			.righ{
				text-align: left;
				  display: inline-block;
				  float: right;
				  font-size: 12px;
				  line-height: 40px;
				  color: #F60;
				  font-weight: normal;
			}
	  }
	  .order_list_goods{
		  
		  width: 100%;
		    overflow: hidden;
		    margin: auto;
		      margin-bottom: 30upx;
		    background: #f5f5f5;
		    padding-top: 10px;
			padding: 20upx 20upx 40upx 20upx;

			image{
				width: 140upx;
				height: 80upx;
			}
			display: flex;
			align-items: center;
			.name{
				font-size: 26upx;
				padding-left: 20upx;
				width: 70%;
				  float: left;
				  overflow: hidden;
				  display: block;
				    font-size: 14px;
				    line-height: 120%;
				    color: #666;
				    font-weight: normal;
			}
			.pice{
				width: 20%;
				  float: left;
				  overflow: hidden;
				  font-weight: normal;
				  font-size: 14px;
				  line-height: 120%;
				  color: #333;
				  text-align: right;
				transform: translateX(-29upx);
				text{
					display: block
				}
			}
	  }
}
.pic{
	width: 95%;
	  margin: auto;
	  overflow: hidden;
	  border-bottom: 1px solid #eeeeee;
	  height: 50px;
	  text-align: right;
	  font-size: 14px;
	  line-height: 50px;
	  color: #999;
	  font-weight: normal;
	  text{
		  font-size: 18px;
		    line-height: 50px;
		    color: #e41735;
		    font-weight: normal;
	  }
}
.anniu{
	eight: 50px;
	  text-align: right;
	  font-weight: normal;
	  padding-right: 30upx;
	  text{
		  display: inline-block;
		    margin-top: 12px;
		    margin-left: 10px;
		    padding-left: 10px;
		    padding-right: 10px;
		    height: 25px;
		    font-size: 12px;
		    line-height: 25px;
		    color: #666;
		    text-align: center;
		    border-radius: 2px;
		
			&.pay{
			  background: #2895CD;
				color: #fff;
				border: 1px solid #2895CD
		  }
		  &.dis{
			  
		  }
	  }
	  
}
.cop1{
	width: 100%;
	height: 30upx;
	background-color: #f5f5f5;
}
</style>
