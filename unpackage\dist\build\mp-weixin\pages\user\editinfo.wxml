<view><view class="box"><view class="item"><view class="left"><text>姓名</text></view><view class="input"><input placeholder="请输入姓名" placeholder-class="pla" data-event-opts="{{[['input',[['__set_model',['$0','realname','$event',[]],['user']]]]]}}" value="{{user.realname}}" bindinput="__e"/></view></view><view class="item"><view class="left"><text>手机号</text></view><view class="input"><input placeholder="请输入手机号" placeholder-class="pla" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['user']]]]]}}" value="{{user.mobile}}" bindinput="__e"/></view></view><view class="item"><view class="left"><text>性别</text></view><view class="input" style="display:flex;"><text style="display:inline-block;width:168rpx;"></text><u-radio-group bind:input="__e" vue-id="3d5914da-1" placement="column" value="{{radiovalue1}}" data-event-opts="{{[['^input',[['__set_model',['','radiovalue1','$event',[]]]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{radiolist1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-radio vue-id="{{('3d5914da-2-'+index)+','+('3d5914da-1')}}" customStyle="{{({marginBottom:'8px'})}}" label="{{item.name}}" name="{{item.name}}" bind:__l="__l"></u-radio></block></u-radio-group></view></view><view class="item"><view class="left"><text>工作单位</text></view><view class="input"><input placeholder="请输入工作单位" placeholder-class="pla" data-event-opts="{{[['input',[['__set_model',['$0','work','$event',[]],['user']]]]]}}" value="{{user.work}}" bindinput="__e"/></view></view></view><view class="box" style="margin-top:40rpx;"><view class="item"><view class="left"><text>邮箱</text></view><view class="input"><input placeholder="请输入邮箱" placeholder-class="pla" data-event-opts="{{[['input',[['__set_model',['$0','email','$event',[]],['user']]]]]}}" value="{{user.email}}" bindinput="__e"/></view></view><view class="item"><view class="left"><text>地址</text></view><view class="input"><input placeholder="请输入地址" placeholder-class="pla" data-event-opts="{{[['input',[['__set_model',['$0','address','$event',[]],['user']]]]]}}" value="{{user.address}}" bindinput="__e"/></view></view></view><view class="cop"></view><view data-event-opts="{{[['tap',[['subt',['$event']]]]]}}" class="subt" bindtap="__e"><text>提交</text></view></view>