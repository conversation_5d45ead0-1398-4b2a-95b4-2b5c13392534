(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uview-ui/components/u-column-notice/u-column-notice"],{"14b0":function(t,n,e){},"179a":function(t,n,e){"use strict";e.r(n);var i=e("e517"),u=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=u.a},1981:function(t,n,e){"use strict";var i=e("14b0"),u=e.n(i);u.a},"2e4c":function(t,n,e){"use strict";e.d(n,"b",(function(){return u})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return i}));var i={uIcon:function(){return Promise.all([e.e("common/vendor"),e.e("uview-ui/components/u-icon/u-icon")]).then(e.bind(null,"22c1"))}},u=function(){var t=this.$createElement,n=(this._self._c,this.__get_style([this.textStyle])),e=["link","closable"].includes(this.mode);this.$mp.data=Object.assign({},{$root:{s0:n,g0:e}})},o=[]},8086:function(t,n,e){"use strict";e.r(n);var i=e("2e4c"),u=e("179a");for(var o in u)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return u[t]}))}(o);e("1981");var c=e("828b"),r=Object(c["a"])(u["default"],i["b"],i["c"],!1,null,"5e49ff52",null,!1,i["a"],void 0);n["default"]=r.exports},e517:function(t,n,e){"use strict";(function(t){var i=e("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=i(e("b2f2")),o={mixins:[t.$u.mpMixin,t.$u.mixin,u.default],watch:{text:{immediate:!0,handler:function(n,e){t.$u.test.array(n)||t.$u.error("noticebar组件direction为column时，要求text参数为数组形式")}}},computed:{textStyle:function(){var n={};return n.color=this.color,n.fontSize=t.$u.addUnit(this.fontSize),n},vertical:function(){return"horizontal"!=this.mode}},data:function(){return{index:0}},methods:{noticeChange:function(t){this.index=t.detail.current},clickHandler:function(){this.$emit("click",this.index)},close:function(){this.$emit("close")}}};n.default=o}).call(this,e("df3c")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uview-ui/components/u-column-notice/u-column-notice-create-component',
    {
        'uview-ui/components/u-column-notice/u-column-notice-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("8086"))
        })
    },
    [['uview-ui/components/u-column-notice/u-column-notice-create-component']]
]);
