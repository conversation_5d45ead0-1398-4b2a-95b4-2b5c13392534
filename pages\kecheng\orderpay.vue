<template>
	<view class="check_out">
		<view class="order_info">
			<view class="li">
				<text>订单名称：</text>
				<text>{{row.bt}}</text>
			</view>
			<view class="li">
				<text>所选科目：</text>
				<text>{{row.lx}}</text>
			</view>
		</view>
		<view class="coupons_info">
			<text>有 效 期</text>
			<text>365天</text>
		</view>
		<view class="pay_info">
			<view>
				<text>订单原价 </text>
				<text>￥{{row.gmjg}}</text>
			</view>
			<view>
				<text>已 优 惠  </text>
				<text>￥00.00</text>
			</view>
			<view class="last">
				<text>实际支付  </text>
				<text>￥{{row.gmjg}}</text>
			</view>
			<text class="dashed"></text>
			<i class="ico_cir cir_l"></i>
			<i class="ico_cir cir_r"></i>
		</view>
		<view class="submit_btn" @click="buy()">立即支付</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id:0,
				on:0,
				row:{}
			}
		},
		onLoad(e) {
			this.id = e.id || 2;
			this.$api.get('kecheng/row',{id:this.id,'uid':uni.getStorageSync('uid')}).then(res => {
				this.row = res;
			})
		},
		methods: {
			buy(){
				this.$api.get('kecheng/buy',{id:this.id,'uid':uni.getStorageSync('uid')}).then(res => {
					this.$api.tourl('/pages/vip/vip?ordersn='+res);
				})
			}
		}
	}
</script>

<style lang="scss">
page{
	font-size: 32upx;
	  color: #666;
	  background: #f5f5f5;
}
.check_out{
	background: #f5f5f5;
	  height: 100vh;
	  color: #666;
	.order_info{
		padding: 30upx;
		  margin-bottom: 20upx;
		  background: #fff;
		  position: relative;
		.li{
			font-size: 28upx;
			padding-bottom: 20upx;
			text{
				&:last-of-type{
					color: #222;
				}
			}
		}  
	}
	.coupons_info{
		padding: 30upx 20upx;
		margin-bottom: 0.2rem;
		  background: #fff;
		  position: relative;
		 display: flex;
		  justify-content: space-between;
		  font-size: 28upx;
	}
	.pay_info{
		padding: 30upx 20upx;
		margin-bottom: 0.2rem;
		  background: #fff;
		position: relative;
		  justify-content: space-between;
		  font-size: 28upx;
		view{
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-bottom: 10upx;
			text{
				&:last-of-type{
					color: #222;
				}
			}
			&.last{
				margin-top: 70upx;
				text{
					&:last-of-type{
						color: red;
					}
				}
			}
		}
		.dashed{
			width: 100%;
			  height: 2px;
			  border-top: 2px dashed #e9e9e9;
			  position: absolute;
			  top: 62%;
			  margin-top: 0.07rem;
		}  
	}
}
.pay_info .cir_l {
  left: -0.07rem;
}
.pay_info .ico_cir {
  position: absolute;
  display: block;
  width: 20upx;
  height: 20upx;
  border-radius: 10upx;
  background: #e9e9e9;
  top: 60%;
}
.pay_info .cir_r {
  right: -0.07rem;
}
.submit_btn{
	display: block;
	  width: 80%;
	  height: 98upx;
	  line-height:98upx;
	  border-radius: 10upx;
	  background: #bf0f35;
	  color: #fff;
	  text-align: center;
	  margin-left: -40%;
	  position: fixed;
	  bottom: 2%;
	  left: 50%;
}
</style>
