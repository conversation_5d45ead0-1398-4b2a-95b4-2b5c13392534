<view data-event-opts="{{[['tap',[['clickHandler',['$event']]]]]}}" class="u-notice data-v-475fdbf0" bindtap="__e"><block wx:if="{{$slots.icon}}"><slot name="icon"></slot></block><block wx:else><block wx:if="{{icon}}"><view class="u-notice__left-icon data-v-475fdbf0"><u-icon vue-id="b6c7b9d4-1" name="{{icon}}" color="{{color}}" size="19" class="data-v-475fdbf0" bind:__l="__l"></u-icon></view></block></block><swiper class="u-notice__swiper data-v-475fdbf0" disable-touch="{{disableTouch}}" vertical="{{step?false:true}}" circular="{{true}}" interval="{{duration}}" autoplay="{{true}}" data-event-opts="{{[['change',[['noticeChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{text}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="u-notice__swiper__item data-v-475fdbf0"><text class="u-notice__swiper__item__text u-line-1 data-v-475fdbf0" style="{{$root.s0}}">{{item}}</text></swiper-item></block></swiper><block wx:if="{{$root.g0}}"><view class="u-notice__right-icon data-v-475fdbf0"><block wx:if="{{mode==='link'}}"><u-icon vue-id="b6c7b9d4-2" name="arrow-right" size="{{17}}" color="{{color}}" class="data-v-475fdbf0" bind:__l="__l"></u-icon></block><block wx:if="{{mode==='closable'}}"><u-icon vue-id="b6c7b9d4-3" name="close" size="{{16}}" color="{{color}}" data-event-opts="{{[['^click',[['close']]]]}}" bind:click="__e" class="data-v-475fdbf0" bind:__l="__l"></u-icon></block></view></block></view>