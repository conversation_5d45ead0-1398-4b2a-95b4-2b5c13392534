@charset "UTF-8";
.box {
  margin-top: 40rpx;
  width: 89%;
  margin-left: 3%;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 20rpx;
}
.box .text1 {
  display: block;
  width: 100%;
  font-weight: 500;
  font-size: 30rpx;
  color: #333333;
}
.box .sf {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 30rpx;
}
.box .sf .bt {
  color: #499EFB;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  font-size: 20rpx;
  background-color: rgba(73, 158, 251, 0.12);
  padding: 6rpx 20rpx;
}
.box .sf .sft {
  display: flex;
  color: #999999;
  font-size: 20rpx;
}
.box .sf .sft .sdd {
  display: flex;
  align-items: center;
}
.box .sf .sft .sdd text {
  padding-left: 10rpx;
}
.box .sf .sft .sdd:last-of-type {
  padding-left: 10rpx;
}
.box image {
  width: 32rpx;
  height: 32rpx;
}
/**搜索**/
.search-box.data-v-5a5386b1 {
  background: linear-gradient(180deg, #ddf5ff, #f5fcff);
  box-sizing: border-box;
  height: 120rpx;
  padding: 0 32rpx;
  display: flex;
  width: 100vw;
  align-items: center;
}
.seart {
  width: 20%;
  text-align: center;
}
.search-box .search-input.data-v-5a5386b1 {
  align-items: center;
  background-color: #fff;
  border-radius: 44rpx;
  box-sizing: border-box;
  color: #757575;
  display: flex;
  flex-direction: row;
  font-family: PingFang SC;
  font-size: 28rpx;
  font-weight: 400;
  height: 88rpx;
  justify-content: flex-start;
  padding-left: 24rpx;
  width: 80%;
}
.search-box .search-input .search-icon.data-v-5a5386b1 {
  height: 30rpx;
  margin-right: 12rpx;
  width: 30rpx;
}
/**end**/
.search-page.data-v-56a7e7a2 {
  min-height: 100vh;
  width: 100%;
}
.search-page .search-box.data-v-56a7e7a2 {
  background: linear-gradient(180deg, #ddf5ff, #fff);
  box-sizing: border-box;
  height: 140rpx;
  left: 0;
  padding: 20rpx 30rpx 40rpx;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 9;
}
.search-page .search-box .search-input.data-v-56a7e7a2 {
  align-items: center;
  background-color: #fff;
  border-radius: 44rpx;
  box-sizing: border-box;
  color: #757575;
  display: flex;
  flex: 1;
  flex-direction: row;
  font-family: PingFang SC;
  font-size: 28rpx;
  font-weight: 400;
  height: 88rpx;
  justify-content: flex-start;
  padding: 0 24rpx;
}
.search-page .search-box .search-input .search-icon.data-v-56a7e7a2 {
  flex-shrink: 0;
  height: 30rpx;
  margin-right: 12rpx;
  width: 30rpx;
}
.search-page .search-box .search-input .input-item.data-v-56a7e7a2 {
  flex: 1;
}
.search-page .search-box .search-text.data-v-56a7e7a2 {
  color: #1c1e1f;
  flex-shrink: 0;
  font-size: 31rpx;
  margin-left: 20rpx;
}
.search-page .search-dom.data-v-56a7e7a2 {
  height: 140rpx;
  width: 100%;
}
.search-page .history-search.data-v-56a7e7a2 {
  box-sizing: border-box;
  margin-bottom: 80rpx;
  padding: 0 30rpx;
}
.search-page .history-search .history-title.data-v-56a7e7a2 {
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-bottom: 33rpx;
  width: 100%;
}
.search-page .history-search .history-title text.data-v-56a7e7a2 {
  color: #1c1e1f;
  font-size: 35rpx;
  font-weight: 500;
}
.search-page .history-search .history-title image.data-v-56a7e7a2 {
  height: 40rpx;
  width: 40rpx;
}
.search-page .history-search .search-list.data-v-56a7e7a2 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.search-page .history-search .search-list .search-item.data-v-56a7e7a2 {
  background: #f7fafa;
  border-radius: 30rpx;
  box-sizing: border-box;
  color: #1c1e1f;
  font-size: 29rpx;
  font-weight: 400;
  height: 60rpx;
  line-height: 60rpx;
  margin: 0 23rpx 23rpx 0;
  max-width: 230rpx;
  overflow: hidden;
  padding: 0 23rpx;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.search-page .hot-search.data-v-56a7e7a2 {
  box-sizing: border-box;
  padding: 0 30rpx;
  width: 100%;
}
.search-page .hot-search .hot-title.data-v-56a7e7a2 {
  margin-bottom: 40rpx;
}
.search-page .hot-search .hot-title text.data-v-56a7e7a2 {
  color: #1c1e1f;
  font-size: 35rpx;
  font-weight: 500;
}
.search-page .hot-search .hot-title image.data-v-56a7e7a2 {
  height: 40rpx;
  margin-right: 8rpx;
  width: 40rpx;
}
.search-page .hot-search .rank-box.data-v-56a7e7a2 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding-bottom: 50rpx;
  width: 100%;
}
.search-page .hot-search .rank-box .item-rank.data-v-56a7e7a2 {
  background-color: #fff;
  flex: 1;
  margin: 0 20rpx 30rpx 0;
  max-width: calc((100% - 40rpx) / 3);
  min-width: calc((100% - 40rpx) / 3);
  width: calc((100% - 40rpx) / 3);
}
.search-page .hot-search .rank-box .item-rank.data-v-56a7e7a2:nth-child(3n) {
  margin-right: 0;
}
.search-page .search-result.data-v-56a7e7a2 {
  box-sizing: border-box;
  padding: 0 30rpx;
}
.search-page .search-result .search-title.data-v-56a7e7a2 {
  color: #1c1e1f;
  font-size: 35rpx;
  font-weight: 500;
  margin-bottom: 40rpx;
}
.search-page .search-result .result-list .result-item.data-v-56a7e7a2 {
  background-color: #fff;
  border-radius: 12rpx;
  box-sizing: border-box;
  display: flex;
  height: 286rpx;
  margin-bottom: 23rpx;
  width: 100%;
}
.search-page
.search-result
.result-list
.result-item
.cover-box.data-v-56a7e7a2 {
  border-radius: 12rpx;
  flex-shrink: 0;
  height: 286rpx;
  margin-right: 30rpx;
  overflow: hidden;
  width: 192rpx;
}
.search-page
.search-result
.result-list
.result-item
.cover-box
image.data-v-56a7e7a2 {
  display: block;
  height: 100%;
  width: 100%;
}
.search-page
.search-result
.result-list
.result-item
.playlet-info.data-v-56a7e7a2 {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  justify-content: space-between;
  padding: 20rpx 0;
}
.search-page
.search-result
.result-list
.result-item
.playlet-info
.playlet-title.data-v-56a7e7a2 {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  color: #1c1e1f;
  display: -webkit-box;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 42rpx;
  margin-bottom: 23rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}
.search-page
.search-result
.result-list
.result-item
.playlet-info
.playlet-tags.data-v-56a7e7a2 {
  color: #9fa1a1;
  display: flex;
  flex-direction: row;
  font-size: 23rpx;
  font-weight: 400;
  margin-bottom: 60rpx;
}
.search-page
.search-result
.result-list
.result-item
.playlet-info
.playlet-tags
text.data-v-56a7e7a2 {
  align-items: center;
  background-color: #f7fafa;
  border-radius: 8rpx;
  box-sizing: border-box;
  color: #757575;
  display: flex;
  font-size: 23rpx;
  height: 38rpx;
  justify-content: center;
  margin-right: 8rpx;
  padding: 0 15rpx;
}
.search-page
.search-result
.result-list
.result-item
.playlet-info
.playlet-btn.data-v-56a7e7a2 {
  border: 1rpx solid #52d0d4;
  border-radius: 29rpx;
  color: #52d0d4;
  font-size: 32rpx;
  height: 58rpx;
  text-align: center;
  line-height: 58rpx;
  width: 170rpx;
}
.search-page .search-nodata {
  color: #757575;
  font-size: 32rpx;
  font-weight: 400;
  text-align: center;
  height: calc(100vh - 140rpx);
  width: 100%;
}
.search-page .search-nodata image.data-v-56a7e7a2 {
  height: 288rpx;
  width: 288rpx;
}

