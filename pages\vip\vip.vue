<template>
	<view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				ordersn:"",
				row:{}
			}
		},
		onLoad(e) {
			this.ordersn = e.ordersn;
			let t = this;
			this.$api.get('kecheng/getOrderRow',{'ordersn':this.ordersn}).then(res => {
				this.row = res;
				this.$api.get('user/pay', {
					price: res.price,
					'openid': uni.getStorageSync('user').openid,
					'ordersn': this.ordersn
				}).then(res => {
					//#ifdef H5
					var jweixin = require('../../js_sdk/index')
					jweixin.config({
						debug: false,
						appId: res.appId,
						timestamp: res.timeStamp,
						nonceStr: res.nonceStr,
						signature: res.paySign,
						jsApiList: ['chooseWXPay']
					});
					jweixin.ready(() => {
						jweixin.chooseWXPay({
							timestamp: res.timeStamp,
							nonceStr: res.nonceStr,
							package: res.package,
							signType: res.signType,
							paySign: res.paySign,
							success: () => {
								t.$api.post('user/buySuncess', {
									ordersn: t.ordersn
								}).then(res => {
									t.$api.msg("购买成功");
									setTimeout(() => {
										t.$api.tourl('/pages/user/order');
									}, 3000);
								});
							}
						});
					});
					//#endif
				});
			})
		},
		methods: {
			
		}
	}
</script>

<style>

</style>
