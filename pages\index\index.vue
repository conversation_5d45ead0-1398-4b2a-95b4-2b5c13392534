<template>
	<view class="content">
		<view class="top">
			<view class="left">
				<image src="/static/logo.png"></image>
			</view>
			<view class="right" @click="$api.tourl('/pages/search/search')">
				<view class="inpuit">
					<image src="/static/search.png"></image>
					<text>国家开放大学</text>
				</view>
			</view>
		</view>
		<view class="sdd3"></view>
		<view class="focus">
			<view class="focus2">
				<swiper class="screen-swiper" :dots-class="'custom-dots'" :autoplay="true" interval="2000"
					duration="500" indicator-dots="true" indicator-color="rgba(255,255,255,0.7)"
					indicator-active-color="#fff">
					<swiper-item v-for="(item, index) in focus" :key="index">
						<image :src="item.fmtp"  mode="aspectFill" @click="setImgFocus(index)"/>
					</swiper-item>
				</swiper>
			</view>
		</view>
		<view class="wap">
			<view class="laba">
				<view class="slef" @click="toadv(text1[0])">
					<image src="/static/laba.png"></image>
					<view class="acd"><u-notice-bar :text="text1[0].bt" :fontSize="12" :speed="15"></u-notice-bar>
					</view>
				</view>
				<!-- <image src="/static/right.png"></image> -->
			</view>
		</view>
		<view class="navs">
			<view class="item" @click="$api.tourl('/pages/ziliao/ziliao?tab=xljy')">
				<image src="/static/s1.png"></image>
				<text>学历教育</text>
			</view>
			<view class="item" @click="$api.tourl('/pages/ziliao/ziliao?tab=qypx')">
				<image src="/static/s5.png"></image>
				<text>企业培训</text>
			</view>
			
			<view class="item" @click="$api.tourl('/pages/ziliao/ziliao?tab=zyzg')">
				<image src="/static/s3.png"></image>
				<text>职业资格</text>
			</view>
			<view class="item" @click="$api.tourl('/pages/ziliao/ziliao?tab=zcps')">
				<image src="/static/s2.png"></image>
				<text>职称评审</text>
			</view>
			
		
			<view class="item" @click="toadv(c)" v-for="(c,k) in navs">
				<image :src="c.tb"></image>
				<text>{{c.mc}}</text>
			</view>
			<!--
			<view class="item" @click="$api.tourl('/pages/ziliao/ziliao?tab=zgzs')">
				<image src="/static/s4.png"></image>
				<text>资格证书</text>
			</view>
			
			<view class="item" @click="$api.tourl('/pages/ziliao/ziliao')">
				<image src="/static/s6.png"></image>
				<text>全部</text>
			</view>
-->
		</view>
		<view class="adv">
			<image :src="c.fmtp" v-for="(c,k) in advs1" @click="toadv(c)"></image>
		</view>
		<view class="items">
			<view class="backgrounds">
				<view class="stitle">
					<image src="../../static/background1.png" mode=""></image>
					<text class="sleft s1" @click="$api.tourl('/pages/kecheng/kecheng')">精品课程</text>
		
					<view class="sright" @click="t2url()">
						<text>查看全部</text>
						<u-icon name="arrow-right" color="#869198" size="14"></u-icon>
					</view>
				</view>
		
				<view class="box1">
					<!-- <image src="../../static/background_bottom.png" mode="" class="image"></image> -->
		
					<view class="ul">
						<text :class="'' == klx ? 'act' : ''" @click="sekbCat('')">{{'全部'}}</text>
		
						<text :class="klx == c.lx ? 'act' : ''" v-for="(c,k) in kcat"
							@click="sekbCat(c.lx)">{{c.lx}}</text>
		
					</view>
					<view class="k3">
						<view class="item3" @click="$api.tourl('/pages/kecheng/show?id='+c.id)"
							v-for="(c,k) in kecheng">
							<image :src="c.fmtp" mode="aspectFill"></image>
							<view class="text2">
								{{c.bt}}
							</view>
							<view class="firs">
								<view class="sdd">
									<text>{{c.ygm}}</text><text>人已购买</text>
								</view>
							</view>
		
							<view class="btn">
								<view class="bbb">免费试听</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		
		<view class="adv3">
			<image v-for="(c,k) in advs2" :src="c.fmtp" @click="toadv(c)" mode=""></image>
		</view>

		<view class="items">
			<view class="background">
				<view class="stitle">
					<image src="../../static/background.png" mode=""></image>
					<text class="sleft s1" @click="$api.tourl('/pages/baoming/baoming')">在线咨询</text>
		
					<view class="sright" @click="$api.tourl('/pages/baoming/baoming')">
						<text>查看全部</text>
						<u-icon name="arrow-right" color="#869198" size="14"></u-icon>
					</view>
				</view>
		
				<view class="box1">
					<view class="ul">
						<text :class="'' == blx ? 'act' : ''" @click="setbCat('')">{{'全部'}}</text>
						<text v-for="(c,k) in baocats" :class="c.lx == blx ? 'act' : ''"
							@click="setbCat(c.lx)">{{c.lx}}</text>
					</view>
		
					<view class="giid" v-for="(c,k) in baolist">
						<view class="giid_item">
							<image :src="c.fmtp" mode="" @click="tonbap(c.bmdz)"></image>
							<view class="slf">
								<text class="text1" @click="tonbap(c.bmdz)">{{c.bt}}</text>
								<text class="button" @click="tonbap(c.bmdz)">详情咨询</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<view class="items">
			<view class="backgroundss">
				<view class="stitle">
					<image src="../../static/background2.png" mode=""></image>
					<text class="sleft s1" @click="$api.tourl('/pages/news/news')">学习资讯</text>

					<view class="sright" @click="$api.tourl('/pages/news/news')">
						<text>查看全部</text>
						<u-icon name="arrow-right" color="#869198" size="14"></u-icon>
					</view>
				</view>

				<view class="box1">
					<view class="ul">
						<text :class="'' == tab ? 'act' : ''" @click="setNewscat('')">{{'全部'}}</text>

						<text :class="c.lx == tab ? 'act' : ''" v-for="(c,k) in newscat"
							@click="setNewscat(c.lx)">{{c.lx}}</text>

					</view>

					<view class="study">
						<view class="bf5" v-for="(c,k) in newslist" @click="tourlIxun(c)">
							<text class="st text1">{{c.bt}}</text>
							<view class="sf9">
								<text class="s11">{{c.lx}}</text>
								<view class="sdd">
									<u-icon name="eye" color="#626164" size="18"></u-icon>
									<text>{{c.lls}}</text>
									<u-icon name="clock" color="#626164" size="18"></u-icon>
									<text>{{c.optdt}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="mark" v-if="showmsg">
			<view class="box5">
				<view class="dd5">
					<view class="close" @click="setshowmsg()">
						<u-icon name="close" color="#fff" size="17"></u-icon>
					</view>
					<image src=""></image>
					<view class="text">您有新的未读消息...</view>
					<view class="but" @click="$api.tourl('/pages/msg/msg')">点击查看</view>
				</view>
			</view>
		</view>
		<view class="cop"></view>
		<Footer :act='"index"'></Footer>
	</view>
</template>

<script>
	import Footer from "@/components/footer.vue";
	export default {
		components: {
			Footer
		},
		data() {
			return {
				text1: [{
					"bt": ""
				}],
				focus: [],
				advs1: [],
				cats: [],
				tab: "",
				user: {},
				navs: [],
				certList:[],
				newslist: [],
				showmsg: false,
				advs2: [],
				baocats: [],
				blx: "",
				baolist: [],
				klx: "",
				kecheng: [],
				kcat: [],
				news: [],
				newscat: [],

				position: 'bottomRight'
			}
		},
		onShow() {
			this.$api.get('user/getUser', {
				"uid": uni.getStorageSync("uid")
			}).then(res => {
				if(!res.mobile){
					//this.$api.tourl('/pages/user/mbind');
				}
				this.user = res;
				uni.setStorageSync('user', res);
				this.$api.get('notice/getUser', {
					uid: uni.getStorageSync('uid'),
					"openid": this.user.openid
				}).then(res => {
					if (res) {
						//消息
						this.showmsg = true;
					}
				})
			});
			uni.setNavigationBarTitle({
				title: "宁教通-首页"
			})
		},
		onLoad() {
			uni.setNavigationBarTitle({
				title: "宁教通-首页"
			})
			this.$api.get('user/authorize',null).then(res => {
				/*
				this.$api.get('user/getCourseCertList',null).then(res => {
					this.certList = res;
				})
				*/
			});	
			this.$api.get('user/getMuint', {
				'type': '首页菜单'
			}).then(res => {
				this.navs = res;
			})
			this.$api.get('config/getFocus').then(res => {
				this.focus = res;
			})
			this.$api.get('config/getAdv', {
				'type': 1
			}).then(res => {
				this.advs1 = res;
			})
			this.$api.get('config/getAdv', {
				'type': 2
			}).then(res => {
				this.advs2 = res;
			})
			this.$api.get('config/getAdv', {
				'type': 6
			}).then(res => {
				this.text1 = res;
			})
			this.$api.get('zixun/getCats').then(res => {
				this.cats = res;
			})
			this.$api.get('baoming/cats').then(res => {
				this.baocats = res;
			})
			
			this.$api.get('kecheng/cats').then(res => {
				this.kcat = res;
			})
			
			this.$api.get('news/cats').then(res => {
				this.newscat = res;
			})
			this.setNewscat("");
			this.setbCat('');
			this.sekbCat('');
			// #ifdef H5
			this.$api.post('h5/jssdk', {
				url: window.location.href,
				act: "index",
				id: 0,
				uid: uni.getStorageSync("uid")
			}).then(res => {
				if(res){
					this.jssdk = res;
					var jweixin = require('../../js_sdk/index')
					jweixin.config({
						debug: false,
						appId: res.appId,
						timestamp: res.timestamp,
						nonceStr: res.nonceStr,
						signature: res.signature,
						jsApiList: [
							'checkJsApi',
							'onMenuShareTimeline',
							'onMenuShareAppMessage',
							'updateTimelineShareData',
							'updateAppMessageShareData'
						]
					});
					jweixin.ready(() => {
						var shareData = {
							title: "宁教通",
							desc: "成人继续教育信息与服务平台",
							link: res.shareurl,
							imgUrl: "http://njt.nxnjt.com/public/share.png",
							success: (res) => {
								console.log(res);
							},
							cancel: function(res) {
					
							}
						};
						console.log(shareData);
						//分享给朋友接口  
						jweixin.onMenuShareTimeline(shareData);
						//分享到朋友圈接口  
						jweixin.onMenuShareAppMessage(shareData);
					
					});
				}
				
			});
			//#endif
		},
		methods: {
			t2url(){
				if(!this.user.mobile){
					uni.showModal({
						title: '提示：',
						content: '请先完善个人信息，然后点确定再跳转到修改信息页面！',
						success: (res) => {
							if (res.confirm) {
								this.$api.tourl('/pages/user/editinfo');
							} else if (res.cancel) {
								
							}
						}
					});
				}else{
					/*
					this.$api.get('user/getUserApiUrl',{uid:this.user.id,'redirect':3}).then(res => {
						location.href = res;
					})
					*/
				   this.$api.tourl('/pages/kecheng/kecheng');
				}
			},
			tourlIxun(res) {
				//'/pages/news/show?id='+c.id
				if (res && (res.ljdz && (res.ljdz != ''))) {
					location.href = res.ljdz;
					return false;
				} else {
					this.$api.tourl("/pages/news/show?id=" + res.id)
				}
			},
			setImgFocus(e) {
				if (this.focus[e].ljdz == '') {
					this.$api.tourl('/pages/adv/adv?id=' + this.focus[e].id);
				} else if (this.focus[e].ljdz.includes('pages') && !this.focus[e].ljdz.includes('http') && !this.focus[e].ljdz.includes('https')) {
					// Internal navigation for paths containing 'pages' but not http/https
					this.$api.tourl(this.focus[e].ljdz);
				} else {
					location.href = this.focus[e].ljdz;
				}
			},
			toadv(c) {
				if (c.ljdz == '') {
					this.$api.tourl('/pages/adv/adv?id=' + c.id);
				} else if (c.ljdz.includes('pages') && !c.ljdz.includes('http') && !c.ljdz.includes('https')) {
					// Internal navigation for paths containing 'pages' but not http/https
					this.$api.tourl(c.ljdz);
				} else {
					location.href = c.ljdz;
				}
			},
			setshowmsg() {
				this.showmsg = !this.showmsg;
			},
			tonbap(url) {
				if (url != '') {
					if (url.includes('pages') && !url.includes('http') && !url.includes('https')) {
						// Internal navigation for paths containing 'pages' but not http/https
						this.$api.tourl(url);
					} else {
						location.href = url;
					}
				}
			},
			setbCat(lx) {
				this.blx = lx;
				this.$api.get('baoming/getIndex', {
					'lx': lx,
					'limit': 3
				}).then(res => {
					this.baolist = res;
				})
			},
			sekbCat(lx) {
				this.klx = lx;
				this.$api.get('kecheng/getIndex', {
					'lx': lx,
					'limit': 100,
					'page':1
				}).then(res => {
					this.kecheng = res;
				})
			},
			setNewscat(lx) {
				this.tab = lx;
				this.$api.get('news/getIndex', {
					"lx": this.tab
				}).then(res => {
					this.newslist = res;
				})
			}
		}
	}
</script>

<style lang="scss" src="./style.scss"></style>