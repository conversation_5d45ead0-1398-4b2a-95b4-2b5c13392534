page {
  font-family: <PERSON>Fang SC, PingFang SC;
}
.navs {
  margin-top: 40rpx;
  width: 94%;
  margin-left: 3%;
  overflow-x: scroll;
  white-space: nowrap;
}
.navs text {
  display: inline-block;
  white-space: nowrap;
  box-sizing: border-box;
  padding: 10rpx 25rpx;
  background-color: #FFFFFF;
  font-weight: 500;
  font-size: 28rpx;
  color: #555555;
  border-radius: 30rpx;
  margin-right: 30rpx;
}
.navs text.act {
  color: #fff;
  background: linear-gradient(360deg, #2D9AFE 0%, #47B8FF 100%);
}
.giid {
  display: flex;
  margin-top: 40rpx;
  width: 96%;
  margin-left: 3%;
}
.giid image {
  width: 280rpx;
  height: 200rpx;
  border-radius: 20rpx;
}
.giid .slf {
  background-color: #fff;
  width: 52%;
  border-radius: 0rpx 20rpx 20rpx 0rpx;
  position: relative;
  padding-left: 4%;
}
.giid text {
  display: block;
}
.giid text.text1 {
  font-family: PingFang SC, PingFang SC;
  font-weight: normal !important;
  font-size: 28rpx;
  width: 100%;
  color: #222222;
}
.giid text.button {
  position: absolute;
  background: linear-gradient(360deg, #2D9AFE 0%, #47B8FF 100%);
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  right: 20rpx;
  bottom: 20rpx;
}

