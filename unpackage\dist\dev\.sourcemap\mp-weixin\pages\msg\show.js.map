{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/kecheng_v3/pages/msg/show.vue?bf36", "webpack:///D:/work/kecheng_v3/pages/msg/show.vue?3940", "webpack:///D:/work/kecheng_v3/pages/msg/show.vue?4505", "webpack:///D:/work/kecheng_v3/pages/msg/show.vue?b357", "uni-app:///pages/msg/show.vue", "webpack:///D:/work/kecheng_v3/pages/msg/show.vue?47e6", "webpack:///D:/work/kecheng_v3/pages/msg/show.vue?00f1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Files", "data", "id", "adv", "row", "tab", "onLoad", "methods", "tourl", "location"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6PAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAimB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0BrnB;EACAC;IACAC;IAAAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;MACA;MACA;IACA;MACA;IACA;IACA;MAAA;IAAA;MACA;IACA;EACA;EACAC;IACAC;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxDA;AAAA;AAAA;AAAA;AAA4oC,CAAgB,6lCAAG,EAAC,C;;;;;;;;;;;ACAhqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/msg/show.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/msg/show.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./show.vue?vue&type=template&id=204e1cc4&\"\nvar renderjs\nimport script from \"./show.vue?vue&type=script&lang=js&\"\nexport * from \"./show.vue?vue&type=script&lang=js&\"\nimport style0 from \"./show.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/msg/show.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./show.vue?vue&type=template&id=204e1cc4&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    jyfParser: function () {\n      return import(\n        /* webpackChunkName: \"components/jyf-parser/jyf-parser\" */ \"@/components/jyf-parser/jyf-parser.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./show.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./show.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"top\">\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t{{row.bt}}\r\n\t\t\t</view>\r\n\t\t\t<view class=\"sdd\">\r\n\t\t\t\t<u-icon name=\"clock\" color=\"#999\" size=\"14\"></u-icon>\r\n\t\t\t\t<text>{{row.fssj}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"box\">\r\n\t\t\t\t<view class=\"d\">\r\n\t\t\t\t\t<jyf-parser :html=\"row.nr\" ref=\"article\"></jyf-parser>　\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"adv\">\r\n\t\t\t\t<image :src=\"adv[0].fmtp\" @click=\"tourl()\"></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<Files :files=\"row.files\"></Files>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport jyfParser from \"@/components/jyf-parser/jyf-parser\";\r\n\timport Files from \"@/components/files.vue\";\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tjyfParser,Files\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tid: 0,\r\n\t\t\t\tadv:[],\r\n\t\t\t\trow:{},\r\n\t\t\t\ttab:\"\",\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\tthis.$api.msg(\"数据读取中...\");\r\n\t\t\tthis.id = e.id || 0;\r\n\t\t\tthis.$api.get('notice/show', {\r\n\t\t\t\t'id':this.id,\r\n\t\t\t\t'openid' : uni.getStorageSync(\"user\").openid\r\n\t\t\t}).then(res => {\r\n\t\t\t\tthis.row = res;\r\n\t\t\t})\r\n\t\t\tthis.$api.get('config/getAdv',{'type':5}).then(res => {\r\n\t\t\t\tthis.adv = res;\r\n\t\t\t})\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttourl(){\r\n\t\t\t\tlocation.href = this.adv[0].ljdz;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.adv{\r\n\tposition: fixed;\r\n\tz-index: 3;\r\n\tright: 10upx;\r\n\tbottom: 100upx;\r\n\theight: 300upx;\r\n\twidth: 300upx;\r\n\timage{\r\n\t\twidth: 300upx;\r\n\t\theight: 300upx;\r\n\t}\r\n}\t\r\n.top{\r\n\twidth: 100%;\r\n\theight: 500upx;\r\n\tbackground: linear-gradient(to bottom, #bfdbfc, #fff);\r\n\tborder-radius: 0px 0px 0px 0px;\r\n\t.title{\r\n\t\tpadding: 30upx 40upx 20upx 40upx;\r\n\t\tfont-family: PingFang SC, PingFang SC;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 34upx;\r\n\t\tcolor: #333;\r\n\t}\r\n\t.sdd{\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding-left: 40upx;\r\n\t\timage{\r\n\t\t\twidth: 60upx;\r\n\t\t\theight: 60upx;\r\n\t\t}\r\n\t\ttext{\r\n\t\t\tfont-family: PingFang SC, PingFang SC;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tfont-size: 30upx;\r\n\t\t\tpadding-left: 10upx;\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\t}\r\n\t.box{\r\n\t\twidth: 96%;\r\n\t\tmargin-left: 2%;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 16upx;\r\n\t\theight: 50vh;\r\n\t\tmargin-top: 30upx;\r\n\t\t.d{\r\n\t\t\tpadding: 30upx;\r\n\t\t\tfont-size: 30upx;\r\n\t\t}\r\n\t}\r\n}\t\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./show.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./show.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1735872264388\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}