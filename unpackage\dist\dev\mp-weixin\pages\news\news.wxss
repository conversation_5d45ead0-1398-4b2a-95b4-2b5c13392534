@charset "UTF-8";
page {
  font-family: PingFang SC, PingFang SC;
}
.navs {
  margin-top: 20rpx;
  width: 100%;
  margin-left: 10rpx;
  overflow-x: scroll;
  white-space: nowrap;
}
.navs text {
  display: inline-block;
  white-space: nowrap;
  box-sizing: border-box;
  padding: 10rpx 25rpx;
  background-color: #FFFFFF;
  font-weight: 500;
  font-size: 28rpx;
  color: #555555;
  border-radius: 30rpx;
  margin-right: 30rpx;
}
.navs text.act {
  color: #fff;
  background: linear-gradient(360deg, #2D9AFE 0%, #47B8FF 100%);
}
.box {
  margin-top: 40rpx;
  width: 89%;
  margin-left: 3%;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 20rpx;
}
.box .text1 {
  display: block;
  width: 100%;
  font-weight: 500;
  font-size: 30rpx;
  color: #333333;
}
.box .sf {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 30rpx;
}
.box .sf .bt {
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  font-size: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #222222;
}
.box .sf .sft {
  display: flex;
  color: #999999;
  font-size: 20rpx;
}
.box .sf .sft .sdd {
  display: flex;
  align-items: center;
}
.box .sf .sft .sdd text {
  padding-left: 10rpx;
}
.box .sf .sft .sdd:last-of-type {
  padding-left: 10rpx;
}
.box image {
  width: 40rpx;
  height: 40rpx;
}
/**弹窗***/
.mack {
  width: 100%;
  height: 100vh;
  z-index: 12;
  position: fixed;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.42);
}
.shimw {
  width: 100%;
  overflow: hidden;
  z-index: 15;
  min-height: 30vh;
  position: fixed;
  top: 0;
  background-color: #fff;
  border-radius: 0rpx 0rpx 20rpx 20rpx;
}
.shimw .stt {
  width: 160rpx;
  height: 52rpx;
  background-color: rgba(73, 158, 251, 0.72);
  font-size: 28rpx;
  display: flex;
  color: #fff;
  align-items: center;
  border-radius: 40rpx;
  padding: 2rpx 16rpx;
  justify-content: center;
  margin-top: 20rpx;
  margin-left: 20rpx;
}
.shimw .stt text {
  padding-right: 10rpx;
}
.shimw .xxx {
  font-size: 28rpx;
  color: #333;
  margin-top: 30rpx;
}
.shimw .xxx .sright {
  width: 75%;
  float: right;
  position: relative;
  height: 1000rpx;
}
.shimw .xxx .sright .box1 {
  max-height: 850rpx;
  overflow-y: scroll;
  padding: 0rpx 20rpx;
}
.shimw .xxx .sright .box1 .item {
  margin-bottom: 30rpx;
}
.shimw .xxx .sright .box1 .item .stt2 {
  color: #000;
  font-weight: bold;
  padding-bottom: 20rpx;
  font-size: 24rpx !important;
}
.shimw .xxx .sright .box1 .item .ul {
  font-size: 24rpx;
  overflow: hidden;
}
.shimw .xxx .sright .box1 .item .ul text {
  display: inline-block;
  width: 31%;
  float: left;
  background-color: #f4f4f4;
  text-align: center;
  margin-bottom: 30rpx;
  padding: 10rpx 0rpx;
  border-radius: 2rpx;
  margin-right: 2.3%;
}
.shimw .xxx .sright .box1 .item .ul text.on {
  background-color: #2875ff;
  color: #fff;
}
.shimw .xxx .sright .butt {
  position: absolute;
  bottom: 30rpx;
  background-color: #499EFB;
  width: 70%;
  margin-left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  display: flex;
  font-size: 28rpx;
  justify-content: center;
  align-items: center;
  color: #fff;
  height: 70rpx;
  border-radius: 50rpx;
}
.shimw .xxx .left {
  width: 25%;
  background-color: #ececec;
  float: left;
  height: 1000rpx;
  position: relative;
  font-size: 28rpx;
}
.shimw .xxx .left .items {
  max-height: 850rpx;
  overflow-y: scroll;
}
.shimw .xxx .left .items text {
  display: block;
  padding-left: 30rpx;
  line-height: 90rpx;
}
.shimw .xxx .left .items text.act {
  background-color: #fff;
  color: #499EFB;
  position: relative;
  font-weight: bold;
}
.shimw .xxx .left .items text.act::before {
  content: "";
  position: absolute;
  width: 8rpx;
  height: 26rpx;
  background-color: #499efb;
  left: 10rpx;
  top: 36%;
  border-radius: 20rpx;
}
.shimw .xxx .left .fb {
  position: absolute;
  bottom: 50rpx;
  width: 100%;
  left: 0rpx;
  text-align: center;
}
/**弹窗***/

