<view class="search-page data-v-56a7e7a2"><view class="search-box data-v-5a5386b1"><view class="search-input data-v-5a5386b1"><image class="search-icon data-v-5a5386b1" src="/static/search1.png"></image><input type="text" placeholder="请输入搜索词" data-event-opts="{{[['input',[['__set_model',['','keyword','$event',[]]]]]]}}" value="{{keyword}}" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['sear']]]]}}" class="seart" bindtap="__e">搜索</view></view><block wx:if="{{$root.g0}}"><view class="search-nodata f-d-c-c data-v-56a7e7a2"><u-empty vue-id="50cad900-1" mode="data" text="暂无搜索记录" icon="http://cdn.uviewui.com/uview/empty/data.png" bind:__l="__l"></u-empty></view></block><block wx:for="{{list}}" wx:for-item="c" wx:for-index="k"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({c})}}" class="box" bindtap="__e"><text class="text1">{{c.bt}}</text><view class="sf"><text class="bt">{{c.lx}}</text><view class="sft"><view class="sdd"><image src="/static/14.png"></image><text>{{c.views}}</text></view><view class="sdd"><image src="/static/15.png"></image><text>{{c.optdt}}</text></view></view></view></view></block></view>