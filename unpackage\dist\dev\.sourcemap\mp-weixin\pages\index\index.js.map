{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/kecheng_v3/pages/index/index.vue?f670", "webpack:///D:/work/kecheng_v3/pages/index/index.vue?b5d8", "webpack:///D:/work/kecheng_v3/pages/index/index.vue?6aff", "webpack:///D:/work/kecheng_v3/pages/index/index.vue?b3ff", "uni-app:///pages/index/index.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Footer", "data", "text1", "focus", "advs1", "cats", "tab", "user", "navs", "newslist", "showmsg", "advs2", "baocats", "blx", "baolist", "klx", "kecheng", "kcat", "news", "newscat", "position", "onShow", "uni", "uid", "onLoad", "title", "methods", "tourlIxun", "location", "setImgFocus", "toadv", "setshowmsg", "tonbap", "setbCat", "sekbCat", "setNewscat"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACe;;;AAGnE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,6PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzFA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCmNtnB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;QACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;IACA;EACA;EACAC;IAAA;IACA;MACA;IACA;MACA;MACAC;MACA;QACAC;QACA;MACA;QACA;UACA;UACA;QACA;MACA;IACA;EACA;EACAC;IAAA;IACAF;MACAG;IACA;IAEA;MACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;MACA;IACA;IACA;MACA;IACA;MACA;IACA;IACA;MACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;IACA;IACA;EA+CA;EACAC;IACAC;MACA;MACA;QACAC;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;QACAD;MACA;IACA;IACAE;MACA;QACA;MACA;QACAF;MACA;IACA;IACAG;MACA;IACA;IACAC;MACA;QACAJ;MACA;IACA;IACAK;MAAA;MACA;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./style.scss?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\ntry {\n  components = {\n    uNoticeBar: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-notice-bar/u-notice-bar\" */ \"@/uview-ui/components/u-notice-bar/u-notice-bar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.$api.tourl(\"/pages/search/search\")\n    }\n    _vm.e1 = function ($event) {\n      return _vm.$api.tourl(\"/pages/ziliao/ziliao?tab=xljy\")\n    }\n    _vm.e2 = function ($event) {\n      return _vm.$api.tourl(\"/pages/ziliao/ziliao?tab=zcps\")\n    }\n    _vm.e3 = function ($event) {\n      return _vm.$api.tourl(\"/pages/ziliao/ziliao?tab=zyzg\")\n    }\n    _vm.e4 = function ($event) {\n      return _vm.$api.tourl(\"/pages/ziliao/ziliao?tab=zgzs\")\n    }\n    _vm.e5 = function ($event) {\n      return _vm.$api.tourl(\"/pages/ziliao/ziliao?tab=qypx\")\n    }\n    _vm.e6 = function ($event) {\n      return _vm.$api.tourl(\"/pages/ziliao/ziliao\")\n    }\n    _vm.e7 = function ($event) {\n      return _vm.$api.tourl(\"/pages/baoming/baoming\")\n    }\n    _vm.e8 = function ($event) {\n      return _vm.$api.tourl(\"/pages/baoming/baoming\")\n    }\n    _vm.e9 = function ($event) {\n      return _vm.$api.tourl(\"/pages/kecheng/kecheng\")\n    }\n    _vm.e10 = function ($event) {\n      return _vm.$api.tourl(\"/pages/kecheng/kecheng\")\n    }\n    _vm.e11 = function ($event, c) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        c = _temp2.c\n      var _temp, _temp2\n      return _vm.$api.tourl(\"/pages/kecheng/show?id=\" + c.id)\n    }\n    _vm.e12 = function ($event) {\n      return _vm.$api.tourl(\"/pages/news/news\")\n    }\n    _vm.e13 = function ($event) {\n      return _vm.$api.tourl(\"/pages/news/news\")\n    }\n    _vm.e14 = function ($event) {\n      return _vm.$api.tourl(\"/pages/msg/msg\")\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"top\">\r\n\t\t\t<view class=\"left\">\r\n\t\t\t\t<image src=\"/static/logo.png\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"right\" @click=\"$api.tourl('/pages/search/search')\">\r\n\t\t\t\t<view class=\"inpuit\">\r\n\t\t\t\t\t<image src=\"/static/search.png\"></image>\r\n\t\t\t\t\t<text>国家开放大学</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"sdd3\"></view>\r\n\t\t<view class=\"focus\">\r\n\t\t\t<view class=\"focus2\">\r\n\t\t\t\t<!-- <u-swiper :list=\"focus\" :keyName='\"fmtp\"' @click=\"setImgFocus\" :radius=\"10\" indicatorMode=\"dot\"\r\n\t\t\t\t\tindicator></u-swiper> -->\r\n\r\n\t\t\t\t<swiper class=\"screen-swiper\" :dots-class=\"'custom-dots'\" :autoplay=\"true\" interval=\"2000\"\r\n\t\t\t\t\tduration=\"500\" indicator-dots=\"true\" indicator-color=\"rgba(255,255,255,0.7)\"\r\n\t\t\t\t\tindicator-active-color=\"#fff\">\r\n\t\t\t\t\t<swiper-item v-for=\"(item, index) in focus\" :key=\"index\">\r\n\t\t\t\t\t\t<image :src=\"item.fmtp\" mode=\"aspectFill\" />\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</swiper>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"wap\">\r\n\t\t\t<view class=\"laba\">\r\n\t\t\t\t<view class=\"slef\" @click=\"toadv(text1[0])\">\r\n\t\t\t\t\t<image src=\"/static/laba.png\"></image>\r\n\t\t\t\t\t<view class=\"acd\"><u-notice-bar :text=\"text1[0].bt\" :fontSize=\"12\" :speed=\"15\"></u-notice-bar>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <image src=\"/static/right.png\"></image> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"navs\">\r\n\t\t\t<view class=\"item\" @click=\"$api.tourl('/pages/ziliao/ziliao?tab=xljy')\">\r\n\t\t\t\t<image src=\"/static/s1.png\"></image>\r\n\t\t\t\t<text>学历教育</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" @click=\"$api.tourl('/pages/ziliao/ziliao?tab=zcps')\">\r\n\t\t\t\t<image src=\"/static/s2.png\"></image>\r\n\t\t\t\t<text>职称评审</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" @click=\"$api.tourl('/pages/ziliao/ziliao?tab=zyzg')\">\r\n\t\t\t\t<image src=\"/static/s3.png\"></image>\r\n\t\t\t\t<text>职业资格</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" @click=\"$api.tourl('/pages/ziliao/ziliao?tab=zgzs')\">\r\n\t\t\t\t<image src=\"/static/s4.png\"></image>\r\n\t\t\t\t<text>资格证书</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" @click=\"$api.tourl('/pages/ziliao/ziliao?tab=qypx')\">\r\n\t\t\t\t<image src=\"/static/s5.png\"></image>\r\n\t\t\t\t<text>企业培训</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" @click=\"toadv(c)\" v-for=\"(c,k) in navs\">\r\n\t\t\t\t<image :src=\"c.tb\"></image>\r\n\t\t\t\t<text>{{c.mc}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" @click=\"$api.tourl('/pages/ziliao/ziliao')\">\r\n\t\t\t\t<image src=\"/static/s6.png\"></image>\r\n\t\t\t\t<text>全部</text>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t\t<view class=\"adv\">\r\n\t\t\t<image :src=\"c.fmtp\" v-for=\"(c,k) in advs1\" @click=\"toadv(c)\"></image>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"items\">\r\n\t\t\t<view class=\"background\">\r\n\t\t\t\t<view class=\"stitle\">\r\n\t\t\t\t\t<image src=\"../../static/background.png\" mode=\"\"></image>\r\n\t\t\t\t\t<text class=\"sleft s1\" @click=\"$api.tourl('/pages/baoming/baoming')\">在线报名</text>\r\n\r\n\t\t\t\t\t<view class=\"sright\" @click=\"$api.tourl('/pages/baoming/baoming')\">\r\n\t\t\t\t\t\t<text>查看全部</text>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#869198\" size=\"14\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"box1\">\r\n\t\t\t\t\t<view class=\"ul\">\r\n\t\t\t\t\t\t<text :class=\"'' == blx ? 'act' : ''\" @click=\"setbCat('')\">{{'全部'}}</text>\r\n\t\t\t\t\t\t<text v-for=\"(c,k) in baocats\" :class=\"c.lx == blx ? 'act' : ''\"\r\n\t\t\t\t\t\t\t@click=\"setbCat(c.lx)\">{{c.lx}}</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"giid\" v-for=\"(c,k) in baolist\">\r\n\t\t\t\t\t\t<view class=\"giid_item\">\r\n\t\t\t\t\t\t\t<image :src=\"c.fmtp\" mode=\"\" @click=\"tonbap(c.bmdz)\"></image>\r\n\t\t\t\t\t\t\t<view class=\"slf\">\r\n\t\t\t\t\t\t\t\t<text class=\"text1\" @click=\"tonbap(c.bmdz)\">{{c.bt}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"button\" @click=\"tonbap(c.bmdz)\">立刻报名</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"adv3\">\r\n\t\t\t<image v-for=\"(c,k) in advs2\" :src=\"c.fmtp\" @click=\"toadv(c)\" mode=\"\"></image>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"items\">\r\n\t\t\t<view class=\"backgrounds\">\r\n\t\t\t\t<view class=\"stitle\">\r\n\t\t\t\t\t<image src=\"../../static/background1.png\" mode=\"\"></image>\r\n\t\t\t\t\t<text class=\"sleft s1\" @click=\"$api.tourl('/pages/kecheng/kecheng')\">体验课</text>\r\n\r\n\t\t\t\t\t<view class=\"sright\" @click=\"$api.tourl('/pages/kecheng/kecheng')\">\r\n\t\t\t\t\t\t<text>查看全部</text>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#869198\" size=\"14\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"box1\">\r\n\t\t\t\t\t<!-- <image src=\"../../static/background_bottom.png\" mode=\"\" class=\"image\"></image> -->\r\n\r\n\t\t\t\t\t<view class=\"ul\">\r\n\t\t\t\t\t\t<text :class=\"'' == klx ? 'act' : ''\" @click=\"sekbCat('')\">{{'全部'}}</text>\r\n\r\n\t\t\t\t\t\t<text :class=\"klx == c.lx ? 'act' : ''\" v-for=\"(c,k) in kcat\"\r\n\t\t\t\t\t\t\t@click=\"sekbCat(c.lx)\">{{c.lx}}</text>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"k3\">\r\n\t\t\t\t\t\t<view class=\"item3\" @click=\"$api.tourl('/pages/kecheng/show?id='+c.id)\"\r\n\t\t\t\t\t\t\tv-for=\"(c,k) in kecheng\">\r\n\t\t\t\t\t\t\t<image :src=\"c.fmtp\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t<view class=\"text2\">\r\n\t\t\t\t\t\t\t\t{{c.bt}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"firs\">\r\n\t\t\t\t\t\t\t\t<view class=\"sdd\">\r\n\t\t\t\t\t\t\t\t\t<text>{{c.ygm}}</text><text>人已购买</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<view class=\"btn\">\r\n\t\t\t\t\t\t\t\t<view class=\"bbb\">免费试听</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"items\">\r\n\t\t\t<view class=\"backgroundss\">\r\n\t\t\t\t<view class=\"stitle\">\r\n\t\t\t\t\t<image src=\"../../static/background2.png\" mode=\"\"></image>\r\n\t\t\t\t\t<text class=\"sleft s1\" @click=\"$api.tourl('/pages/news/news')\">学习资讯</text>\r\n\r\n\t\t\t\t\t<view class=\"sright\" @click=\"$api.tourl('/pages/news/news')\">\r\n\t\t\t\t\t\t<text>查看全部</text>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#869198\" size=\"14\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"box1\">\r\n\t\t\t\t\t<view class=\"ul\">\r\n\t\t\t\t\t\t<text :class=\"'' == tab ? 'act' : ''\" @click=\"setNewscat('')\">{{'全部'}}</text>\r\n\r\n\t\t\t\t\t\t<text :class=\"c.lx == tab ? 'act' : ''\" v-for=\"(c,k) in newscat\"\r\n\t\t\t\t\t\t\t@click=\"setNewscat(c.lx)\">{{c.lx}}</text>\r\n\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"study\">\r\n\t\t\t\t\t\t<view class=\"bf5\" v-for=\"(c,k) in newslist\" @click=\"tourlIxun(c)\">\r\n\t\t\t\t\t\t\t<text class=\"st text1\">{{c.bt}}</text>\r\n\t\t\t\t\t\t\t<view class=\"sf9\">\r\n\t\t\t\t\t\t\t\t<text class=\"s11\">{{c.lx}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"sdd\">\r\n\t\t\t\t\t\t\t\t\t<u-icon name=\"eye\" color=\"#626164\" size=\"18\"></u-icon>\r\n\t\t\t\t\t\t\t\t\t<text>{{c.lls}}</text>\r\n\t\t\t\t\t\t\t\t\t<u-icon name=\"clock\" color=\"#626164\" size=\"18\"></u-icon>\r\n\t\t\t\t\t\t\t\t\t<text>{{c.optdt}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"mark\" v-if=\"showmsg\">\r\n\t\t\t<view class=\"box5\">\r\n\t\t\t\t<view class=\"dd5\">\r\n\t\t\t\t\t<view class=\"close\" @click=\"setshowmsg()\">\r\n\t\t\t\t\t\t<u-icon name=\"close\" color=\"#fff\" size=\"17\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<image src=\"\"></image>\r\n\t\t\t\t\t<view class=\"text\">你有新的未读消息...</view>\r\n\t\t\t\t\t<view class=\"but\" @click=\"$api.tourl('/pages/msg/msg')\">点击查看</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"cop\"></view>\r\n\t\t<Footer :act='\"index\"'></Footer>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport Footer from \"@/components/footer.vue\";\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tFooter\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttext1: [{\r\n\t\t\t\t\t\"bt\": \"\"\r\n\t\t\t\t}],\r\n\t\t\t\tfocus: [],\r\n\t\t\t\tadvs1: [],\r\n\t\t\t\tcats: [],\r\n\t\t\t\ttab: \"\",\r\n\t\t\t\tuser: {},\r\n\t\t\t\tnavs: [],\r\n\t\t\t\tnewslist: [],\r\n\t\t\t\tshowmsg: false,\r\n\t\t\t\tadvs2: [],\r\n\t\t\t\tbaocats: [],\r\n\t\t\t\tblx: \"\",\r\n\t\t\t\tbaolist: [],\r\n\t\t\t\tklx: \"\",\r\n\t\t\t\tkecheng: [],\r\n\t\t\t\tkcat: [],\r\n\t\t\t\tnews: [],\r\n\t\t\t\tnewscat: [],\r\n\r\n\t\t\t\tposition: 'bottomRight'\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.$api.get('user/getUser', {\r\n\t\t\t\t\"uid\": uni.getStorageSync(\"uid\")\r\n\t\t\t}).then(res => {\r\n\t\t\t\tthis.user = res;\r\n\t\t\t\tuni.setStorageSync('user', res);\r\n\t\t\t\tthis.$api.get('notice/getUser', {\r\n\t\t\t\t\tuid: uni.getStorageSync('uid'),\r\n\t\t\t\t\t\"openid\": this.user.openid\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res) {\r\n\t\t\t\t\t\t//消息\r\n\t\t\t\t\t\tthis.showmsg = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t});\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: \"宁教通-首页\"\r\n\t\t\t})\r\n\r\n\t\t\tthis.$api.get('user/getMuint', {\r\n\t\t\t\t'type': '首页菜单'\r\n\t\t\t}).then(res => {\r\n\t\t\t\tthis.navs = res;\r\n\t\t\t})\r\n\t\t\tthis.$api.get('config/getFocus').then(res => {\r\n\t\t\t\tthis.focus = res;\r\n\t\t\t})\r\n\t\t\tthis.$api.get('config/getAdv', {\r\n\t\t\t\t'type': 1\r\n\t\t\t}).then(res => {\r\n\t\t\t\tthis.advs1 = res;\r\n\t\t\t})\r\n\t\t\tthis.$api.get('config/getAdv', {\r\n\t\t\t\t'type': 2\r\n\t\t\t}).then(res => {\r\n\t\t\t\tthis.advs2 = res;\r\n\t\t\t})\r\n\t\t\tthis.$api.get('config/getAdv', {\r\n\t\t\t\t'type': 6\r\n\t\t\t}).then(res => {\r\n\t\t\t\tthis.text1 = res;\r\n\t\t\t})\r\n\t\t\tthis.$api.get('zixun/getCats').then(res => {\r\n\t\t\t\tthis.cats = res;\r\n\t\t\t})\r\n\t\t\tthis.$api.get('baoming/cats').then(res => {\r\n\t\t\t\tthis.baocats = res;\r\n\t\t\t})\r\n\t\t\tthis.$api.get('kecheng/cats').then(res => {\r\n\t\t\t\tthis.kcat = res;\r\n\t\t\t})\r\n\t\t\tthis.$api.get('news/cats').then(res => {\r\n\t\t\t\tthis.newscat = res;\r\n\t\t\t})\r\n\t\t\tthis.setNewscat(\"\");\r\n\t\t\tthis.setbCat('');\r\n\t\t\tthis.sekbCat('');\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.$api.post('h5/jssdk', {\r\n\t\t\t\turl: window.location.href,\r\n\t\t\t\tact: \"index\",\r\n\t\t\t\tid: 0,\r\n\t\t\t\tuid: uni.getStorageSync(\"uid\")\r\n\t\t\t}).then(res => {\r\n\t\t\t\tthis.jssdk = res;\r\n\t\t\t\tvar jweixin = require('../../js_sdk/index')\r\n\t\t\t\tjweixin.config({\r\n\t\t\t\t\tdebug: false,\r\n\t\t\t\t\tappId: res.appId,\r\n\t\t\t\t\ttimestamp: res.timestamp,\r\n\t\t\t\t\tnonceStr: res.nonceStr,\r\n\t\t\t\t\tsignature: res.signature,\r\n\t\t\t\t\tjsApiList: [\r\n\t\t\t\t\t\t'checkJsApi',\r\n\t\t\t\t\t\t'onMenuShareTimeline',\r\n\t\t\t\t\t\t'onMenuShareAppMessage',\r\n\t\t\t\t\t\t'updateTimelineShareData',\r\n\t\t\t\t\t\t'updateAppMessageShareData'\r\n\t\t\t\t\t]\r\n\t\t\t\t});\r\n\t\t\t\tjweixin.ready(() => {\r\n\t\t\t\t\tvar shareData = {\r\n\t\t\t\t\t\ttitle: \"宁教通\",\r\n\t\t\t\t\t\tdesc: \"成人继续教育信息与服务平台\",\r\n\t\t\t\t\t\tlink: res.shareurl,\r\n\t\t\t\t\t\timgUrl: \"http://njt.nxnjt.com/public/share.png\",\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tcancel: function(res) {\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t};\r\n\t\t\t\t\tconsole.log(shareData);\r\n\t\t\t\t\t//分享给朋友接口  \r\n\t\t\t\t\tjweixin.onMenuShareTimeline(shareData);\r\n\t\t\t\t\t//分享到朋友圈接口  \r\n\t\t\t\t\tjweixin.onMenuShareAppMessage(shareData);\r\n\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t\t//#endif\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttourlIxun(res) {\r\n\t\t\t\t//'/pages/news/show?id='+c.id\r\n\t\t\t\tif (res && (res.ljdz && (res.ljdz != ''))) {\r\n\t\t\t\t\tlocation.href = res.ljdz;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$api.tourl(\"/pages/news/show?id=\" + res.id)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsetImgFocus(e) {\r\n\t\t\t\tif (this.focus[e].ljdz == '') {\r\n\t\t\t\t\tthis.$api.tourl('/pages/adv/adv?id=' + this.focus[e].id);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlocation.href = this.focus[e].ljdz;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttoadv(c) {\r\n\t\t\t\tif (c.ljdz == '') {\r\n\t\t\t\t\tthis.$api.tourl('/pages/adv/adv?id=' + c.id);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlocation.href = c.ljdz;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsetshowmsg() {\r\n\t\t\t\tthis.showmsg = !this.showmsg;\r\n\t\t\t},\r\n\t\t\ttonbap(url) {\r\n\t\t\t\tif (url != '') {\r\n\t\t\t\t\tlocation.href = url;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsetbCat(lx) {\r\n\t\t\t\tthis.blx = lx;\r\n\t\t\t\tthis.$api.get('baoming/getIndex', {\r\n\t\t\t\t\t'lx': lx,\r\n\t\t\t\t\t'limit': 3\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthis.baolist = res;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsekbCat(lx) {\r\n\t\t\t\tthis.klx = lx;\r\n\t\t\t\tthis.$api.get('kecheng/getIndex', {\r\n\t\t\t\t\t'lx': lx,\r\n\t\t\t\t\t'limit': 3\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthis.kecheng = res;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsetNewscat(lx) {\r\n\t\t\t\tthis.tab = lx;\r\n\t\t\t\tthis.$api.get('news/getIndex', {\r\n\t\t\t\t\t\"lx\": this.tab\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthis.newslist = res;\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" src=\"./style.scss\"></style>"], "sourceRoot": ""}