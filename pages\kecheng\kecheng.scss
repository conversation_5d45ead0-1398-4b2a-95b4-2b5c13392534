page {
	font-family: '<PERSON>Fang'; 
	background-color: #f6f7fd;
}
.search-top{
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20upx 20upx;
	background-color: #fff;
	.all{
		display: inline-block;
		color: #017BFF;
		background: rgba(1, 123, 255, 0.12);
		border: 1px solid #017BFF;
		padding: 2px 8px;
		font-weight: 500;
		font-size: 28rpx;
		color: #555555;
		border-radius: 10rpx;
		margin-right: 30upx;
	}
	.sleft{
		width: 80%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		input{
			width: 80%;
			font-size: 28upx;
			background-color: #f6f7fd;
			padding: 12upx;
			border-radius: 10upx;
		}
		.sub{
			color: #017BFF;
			font-size: 30upx;
		}
	}
}
.navs{
	margin-top: 20rpx;
	width: 100%;
	margin-left: 20rpx;
	overflow-x: scroll;
	display: none;
	white-space: nowrap;
	text{
		display: inline-block;
		white-space: nowrap;
		box-sizing:border-box;
		padding: 8rpx 20rpx;
		background-color: #FFFFFF;
		font-weight: 500;
		font-size: 28rpx;
		color: #555555;
		border-radius: 10rpx;
		margin-right: 30upx;
		&.act{
			color: #017BFF;
			background: rgba(1,123,255,0.12);
			border: 2rpx solid #017BFF;
		}
	}
}
.box1{
	margin-top: 20rpx;
	width: 100%;
	// margin-left: 3%;
	padding: 20rpx;
	box-sizing: border-box;
	
	.item{
		background-color: #fff;
		width: 48%;
		float: left;
		border-radius: 20upx;
		margin-bottom: 30rpx;
		box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(0,0,0,0.05);
		
		&:nth-child(2n){
			float: right;
		}
		.text2{
			padding: 10rpx 20rpx;
			font-weight: bold;
			font-size: 28upx;
			color: #333333;
			box-sizing: border-box;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			overflow:hidden;
		}
		image{
			width: 100%;
			height: 220rpx;
			border-radius: 20upx;
		}
		.firs{
			padding: 0 20upx;
			box-sizing: border-box;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.sdd{
				font-size: 24upx;
				color: #F56711;
				text{
					&:last-of-type{
						color: #999;
					}
				}
			}
			// .bbb{
			// 	background: linear-gradient( 360deg, #2D9AFE 0%, #47B8FF 100%);
			// 	border-radius: 66upx;
			// 	font-weight: bold;
			// 	font-size: 24rpx;
			// 	color: #FFFFFF;
			// 	padding: 12upx 26upx;
			// }
		}
		
		.btn {
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 20rpx;
			box-sizing: border-box;
			
			.bbb{
				width: 200rpx;
				background: linear-gradient( 360deg, #2D9AFE 0%, #47B8FF 100%);
				border-radius: 66upx;
				font-weight: bold;
				font-size: 24rpx;
				color: #FFFFFF;
				padding: 12rpx 26rpx;
				text-align: center;
			}
		}
	}
}

/**弹窗***/
.mack{
	width: 100%;
	height: 100vh;
	z-index: 12;
	position: fixed;
	bottom: 0;
	background-color: rgba(#000, .42);
}
.shimw{
	width: 100%;
	overflow: hidden;
	z-index: 15;
	min-height: 30vh;
	position: fixed;
	top: 0;
	background-color: #fff;
	border-radius: 0upx 0upx 20upx 20upx;
	.stt{
		width: 160upx;
		height: 52upx;
		background-color: rgba(#499EFB, .72);
		font-size: 28upx;
		display: flex;
		color: #fff;
		align-items: center;
		border-radius: 40upx;
		padding: 2upx 16upx;
		justify-content: center;
		margin-top: 20upx;
		margin-left: 20upx;
		text{
			padding-right: 10upx;
		}
	}
	.xxx{
		font-size: 28upx;
		color: #333;
		margin-top: 20upx;
		.sright{
			width: 75%;
			float: right;
			position: relative;
			height: 1000upx;
			.box2{
				max-height: 850upx;
				overflow-y: scroll;
				padding: 0upx 20upx;
				.item4{
					margin-bottom: 30upx;
					.stt2{
						color: #000;
						font-weight: bold;
						padding-bottom: 20upx;
						font-size: 24upx !important;
					}
					.ul{
						font-size: 22rpx;
						overflow: hidden;
						text{
							display: inline-block;
							float: left;
							background-color: #F5F7FB;
							text-align: center;
							margin-bottom: 30upx;
							padding: 15rpx 10upx;
							border-radius: 10rpx;
							margin-right: 2.3%;
							font-weight: 500;
							
							&.on{
								background: #D6EAFE;
								color: #2D9AFE;
							}
						}
					}
				}
			}
			.close{
				position: absolute;
				bottom:  40upx;
				left: -130upx;
				color: #000;
				font-size: 28upx;
				background-color: #fff;
				padding: 10upx 20upx;
				border-radius: 120upx;
				border: 1px solid #000;
			}
			.butt{
				position: absolute;
				bottom: 30upx;
				background-color: #499EFB;
				width: 70%;
				margin-left: 50%;
				transform: translateX(-50%);
				display: flex;
				font-size: 28upx;
				justify-content: center;
				align-items: center;
				color: #fff;
				height: 70upx;
				border-radius: 50upx;
			}
		}
		.left{
			width: 25%;
			background-color: #F5F7FB;
			float: left;
			height: 1000upx;
			position: relative;
			font-size: 28upx;
			.items{
				max-height: 850upx;
				overflow-y: scroll;
				text{
					display: block;
					padding-left: 30upx;
					line-height: 90upx;
					&.act{
						background-color: #fff;
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 28rpx;
						color: #2D9AFE;
						position: relative;
						
						&::before{
							content: "";
							position: absolute;
							width: 8upx;
							height: 40rpx;
							background-color: rgba(#499EFB, 1);
							left: 0;
							top: 30%;
							border-radius: 20upx;
						}
					}
				}
			}
			.fb{
				position: absolute;
				bottom: 50upx;
				width: 100%;
				left: 0upx;
				text-align: center;
			}
		}
	}
}
/**弹窗***/