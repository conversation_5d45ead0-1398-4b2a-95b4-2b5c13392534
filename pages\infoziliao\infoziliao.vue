<template>
	<view>
		<view class="navs">
			<text @click="setCat('')" :class="'' == cname ? 'act' : ''">全部</text>
			<text v-for="(c,k) in cats" @click="setCat(c.lx)" :class="c.lx == cname ? 'act' : ''">{{c.lx}}</text>
		</view>
		
		<view class="training-list">
			<view class="training-item" v-for="(c,k) in list" :key="k" @click="tourlIxun(c)">
				<image class="training-image" :src="c.fmtp || '/static/default-building.jpg'"></image>
				<view class="training-content">
					<view class="training-title text-ellipsis">{{ c.bt }}</view>
					<view class="training-info">
						<view class="training-tag">{{c.lx}}</view>
						<view class="training-stats">
							<view class="stat-item">
								<image src="/static/14.png" style="width: 38upx;height: 38upx;"></image>
								<text>{{c.lls || ''}}</text>
							</view>
							<view class="stat-item">
								<image src="/static/15.png"></image>
								<text style="padding-left: 10upx;">{{c.optdt || ''}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<view class="cop">{{nodata}}</view>
		<Footer :act="act"></Footer>
	</view>
</template>

<script>
	import Footer from "@/components/footer.vue";
	export default {
		components: {
			Footer
		},
		data() {
			return {
				cname:"",
				cats:[],
				list:[],
				page:1,
				act:"ziliao",
				ck: 0,
				nodata:"",
				lx:"",
				tab:"",
			}
		},
		onLoad(e) {
			if(e.lx == 'e'){
				this.lx = '学习资料';
			}
			this.$api.msg("数据读取中...");
			this.$api.get('news/infoziliaocat').then(res => {
				this.cats = res;
			})
			this.getData(this.lx);
			this.$api.get('user/getUserApiUrl',{uid:uni.getStorageSync("uid"),'redirect':3}).then(res => {
				if(res){
					uni.setStorageSync('apikcurl' , res);
				}
			})	
		},
		onReachBottom(){
			if(this.nodata == ""){
				this.page += 1;
				this.$api.msg("数据读取中...");
				this.getData(this.cname);
			}
		},
		methods: {
			tourlIxun(res){
				if(res && (res.ljdz && (res.ljdz != ''))){
					location.href = res.ljdz;
					return false;
				}else{
					this.$api.tourl('/pages/infoziliao/show?id='+res.id)
				}
			},
			
			setCat(lx){
				this.page = 1;
				this.nodata = "";
				this.cname = lx;
				if(lx == ''){
					uni.setNavigationBarTitle({
						title:'宁教通-学习资料'
					})
				}else{
					uni.setNavigationBarTitle({
						title:'宁教通-'+lx
					})
				}
				this.list = [];
				this.getData(lx);
			},
			getData(lx){
				this.cname = lx;
				
				this.$api.get('news/getInfoziliaoList' , {"lx":lx,"page":this.page}).then(res => {
					if(!res || (res.length == 0)){
						this.nodata = "暂无更多数据";
					}else{
						this.list = this.page == 1 ? res : this.list.concat(res);
					}
				})
			},
		}
	}
</script>

<style lang="scss" src="./news2.scss"></style>
