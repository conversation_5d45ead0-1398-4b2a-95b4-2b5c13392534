<template>
	<view>
		<view class="top">
			<video :src="row.spdz" style="width: 100%;height: 400upx;"></video>
		</view>
		<view class="item-rankxt">
			{{row.bt}}
		</view>
		<view class="info" style="display: none;">
			<text>类型：{{row.lx}}</text>
			<text>有效期：{{row.kssj}}</text>
			<text>是否有效：{{row.yesno}}</text>
			<text>价格：{{row.gmjg || "免费"}}</text>
		</view>
		<view class="nr">
			<view class="d" style="padding: 20upx;">
				<jyf-parser :html="row.nr" ref="article"></jyf-parser>　
			</view>
			
		</view>
		<view class="but" @click="tourl()">去选课中心购买</view>
	</view>
</template>

<script>
	import jyfParser from "@/components/jyf-parser/jyf-parser";
	export default {
		components:{jyfParser},
		data() {
			return {
				id:0,
				row:{}
			}
		},
		onLoad(e) {
			this.id = e.id || 2;
			this.$api.get('kecheng/row',{id:this.id,'uid':uni.getStorageSync('uid')}).then(res => {
				this.row = res;
			})
		},
		methods: {
			tourl(){
				var user = uni.getStorageSync('user');
				if(!user.mobile){
					uni.showModal({
						title: '提示：',
						content: '请先完善个人信息，然后点确定再跳转到修改信息页面！',
						success: (res) => {
							if (res.confirm) {
								this.$api.tourl('/pages/user/editinfo');
							} else if (res.cancel) {
								
							}
						}
					});
				}else{
					this.$api.get('user/getUserApiUrl',{uid:user.id,'redirect':3}).then(res => {
						location.href = res;
					})	
				}
			}
		}
	}
</script>

<style lang="scss" src="./show.scss"></style>
