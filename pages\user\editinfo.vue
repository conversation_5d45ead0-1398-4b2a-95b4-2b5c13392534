<template>
	<view>
		<view class="box">
			<view class="item">
				<view class="left">
					<text><text style="color: red">*</text>姓名</text>
				</view>
				<view class="input">
					<input placeholder="请输入姓名" v-model="user.realname" placeholder-class="pla" />
				</view>
			</view>
			<view class="item">
				<view class="left">
					<text><text style="color: red">*</text>手机号</text>
				</view>
				<view class="input">
					<input placeholder="请输入手机号" v-model="user.mobile" placeholder-class="pla" />
				</view>
			</view>
			<view class="item">
				<view class="left">
					<text>性别</text>
				</view>
				<view class="input" style="display: flex;">
					<text style="display: inline-block;width: 168upx;"></text>
					<u-radio-group v-model="radiovalue1" placement="column">
						<u-radio :customStyle="{marginBottom: '8px'}" v-for="(item, index) in radiolist1" :key="index"
							:label="item.name" :name="item.name" >
						</u-radio>
					</u-radio-group>

				</view>
			</view>
			<view class="item">
				<view class="left">
					<text>工作单位</text>
				</view>
				<view class="input">
					<input placeholder="请输入工作单位" v-model="user.work" placeholder-class="pla" />
				</view>
			</view>
		</view>
		<view class="box" style="margin-top: 40upx;">
			<view class="item">
				<view class="left">
					<text>邮箱</text>
				</view>
				<view class="input">
					<input placeholder="请输入邮箱" v-model="user.email" placeholder-class="pla" />
				</view>
			</view>
			<view class="item">
				<view class="left">
					<text>地址</text>
				</view>
				<view class="input">
					<input placeholder="请输入地址" v-model="user.address" placeholder-class="pla" />
				</view>
			</view>

		</view>

		<view class="cop"></view>
		<view class="subt" @click="subt">
			<text>提交</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				uid: uni.getStorageSync('uid') || 0,
				user: {},
				radiolist1: [{
						name: '男',
						disabled: false
					},
					{
						name: '女',
						disabled: false
					},

				],

				radiovalue1: '',

			}
		},
		onShow() {
			this.$api.get('user/getUser', {
				uid: this.uid
			}).then(res => {
				this.user = res;
				if (res.sex) {
					this.radiovalue1 = res.sex == 1 ? '男' : '女';
				}

			})
		},
		onLoad() {

		},
		methods: {
			isAllChineseExtended(str) {
			    const regex = /^[\u3400-\u4DBF\u4E00-\u9FFF\uF900-\uFAFF]+$/;
			    return regex.test(str);
			},
			isPoneAvailable(t) {
				return !!/^[1][1,3,2,4,5,7,8,9][0-9]{9}$/.test(t);
			},
			subt() {
				if (!this.user.realname) {
					this.$api.msg("请输入姓名");
					return false;
				}
				if (!this.isAllChineseExtended(this.user.realname)) {
					this.$api.msg("姓名必须是汉字");
					return false;
				}
				if (this.user.mobile == "") {
					this.$api.msg("请输入手机号");
					return false;
				}
				if (!this.isPoneAvailable(this.user.mobile)) {
					this.$api.msg("请输入有效的手机号!");
					return false;
				}
				this.user.sex = this.radiovalue1 == '男' ? 1 : 2;
				this.$api.post('user/updateUser?uid='+this.user.id, {
					data: this.user
				}).then(res => {
					if(res == -1){
						this.$api.msg("该手机号码已经被注册了！");
					}else{
						this.$api.msg("资料保存成功");
						uni.setStorageSync('user',this.user);
						setTimeout(() => {
							this.$api.tourl('/pages/index/index')
						},2000)
					}
				})
			},
		}
	}
</script>

<style lang="scss">
	page {
		font-family: 'PingFang'; 
	}
	
	.u-radio-group{
		position: relative;
		transform: translateX(20upx);
		.u-radio{
			&:last-of-type{
				position: absolute;
				left: 120upx;		
			}
		}
	}
	.box {
		background-color: #fff;
		width: 94%;
		margin-left: 3%;
		margin-top: 30upx;
		border-radius: 20upx;
		font-family: PingFang SC, PingFang SC;

		.item {
			display: flex;
			padding: 30upx;
			margin-bottom: 30upx;
			border-bottom: 1px solid #F0F0F0;

			.left {
				width: 240upx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 32rpx;
				color: #333333;
			}

			.input {
				text-align: right;
				font-size: 32upx;
				color: #333333;

				input {
					font-size: 32upx;
					color: #333333;
				}

				.pla {
					font-weight: 400;
					font-size: 32upx;
					color: #999999;
				}

				radio {
					transform: scale(.72);
					padding-left: 20upx;
				}
			}

			&:last-of-type {
				border-bottom: none;
			}
		}
	}

	page {
		font-family: PingFang SC, PingFang SC;
		background-color: #F7F8FA;
	}

	.subt {
		background: #2695FF;
		border-radius: 47px 47px 47px 47px;
		text-align: center;
		width: 94%;
		margin-left: 3%;
		height: 90upx;
		line-height: 90upx;
		color: #fff;
		font-size: 32upx;
		position: fixed;
		z-index: 1;
		bottom: 40rpx;
	}
</style>