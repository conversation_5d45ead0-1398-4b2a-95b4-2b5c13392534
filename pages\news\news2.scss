page {
	font-family: 'PingFang'; 
	background-image: url('@/static/bg3.png');
	background-size: 100% 100%;
	min-height: 100vh;
	width: 100%;
}
.navs{
    width: 100%;
  
	margin-top: 20upx;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    padding: 16upx 30upx;
	overflow-x: scroll;

	white-space: nowrap;
	text{
		display: inline-block;
		white-space: nowrap;
		box-sizing:border-box;
		margin-right: 50upx;
		border-radius: 50upx;
		background-color: #f2f9ff;
		padding: 14upx 30upx;
		font-weight: 500;
		font-weight: 500;
		font-size: 28upx;
		color: #333333;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;

        &.act{
            background: #2D9AFE;
            
      
            color: #FFFFFF;
        }
	}
}


.training-list {
    padding: 20upx 30upx;
	margin-top: 0upx;
}

.training-item {
    display: flex;
    background-color: #FFFFFF;
    border-radius: 12upx;
    padding: 20upx;
    margin-bottom: 20upx;
    box-shadow: 0 2upx 10upx rgba(0, 0, 0, 0.05);
}

.training-image {
    width: 160upx;
    height: 140upx;
    border-radius: 8upx;
    margin-right: 20upx;
    flex-shrink: 0;
}

.training-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.training-title {
    font-size: 30upx;
    color: #333;
    font-weight: 500;
    margin-bottom: 30upx;
    line-height: 1.4;
}

.text-ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 490upx;
}

.training-info {

}

.training-tag {
    display: inline-block;
    font-size: 24upx;
    color: #2D9AFE;
    background-color: #F2F9FF;
    padding: 4upx 16upx;
    transform: translateY(-20upx);
    border-radius: 4upx;
}

.training-stats {
    display: flex;
    align-items: center;
}

.stat-item {
    display: flex;
    align-items: center;
    margin-left: 20upx;
    
    image {
        width: 28upx;
        height: 28upx;
        margin-right: 6upx;
    }
    
    text {
        font-size: 24upx;
        color: #999;
    }
}
.cop {
    text-align: center;
    font-size: 28upx;
    color: #999;
    padding: 30upx 0;
}