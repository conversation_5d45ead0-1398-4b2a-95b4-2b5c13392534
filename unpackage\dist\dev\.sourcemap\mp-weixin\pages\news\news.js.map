{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/kecheng_v3/pages/news/news.vue?c599", "webpack:///D:/work/kecheng_v3/pages/news/news.vue?4a89", "webpack:///D:/work/kecheng_v3/pages/news/news.vue?ddc1", "webpack:///D:/work/kecheng_v3/pages/news/news.vue?5eb1", "uni-app:///pages/news/news.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Footer", "data", "cname", "cats", "list", "page", "act", "ck", "nodata", "lx", "tab", "onLoad", "console", "onReachBottom", "methods", "setCat", "uni", "title", "getData"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACyB;;;AAG5E;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAimB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eC8BrnB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACAC;IACA;MACA;IACA;IACA;IACA;IACA;MACA;IACA;IACA;EAEA;EACAC;IACA;MACA;MACA;MACA;IACA;EACA;EACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACAC;IACAC;MACA;MACA;MACA;MACA;QACAC;UACAC;QACA;MACA;QACAD;UACAC;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MAEA;QAAA;QAAA;MAAA;QACA;UACA;UAEA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/news/news.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/news/news.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./news.vue?vue&type=template&id=c454de34&\"\nvar renderjs\nimport script from \"./news.vue?vue&type=script&lang=js&\"\nexport * from \"./news.vue?vue&type=script&lang=js&\"\nimport style0 from \"../ziliao/ziliao.scss?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/news/news.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./news.vue?vue&type=template&id=c454de34&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, c) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        c = _temp2.c\n      var _temp, _temp2\n      return _vm.$api.tourl(\"/pages/news/show?id=\" + c.id)\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./news.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./news.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\r\n\t\t<view class=\"navs\">\r\n\t\t\t<text @click=\"setCat('')\" :class=\"'' == cname ? 'act' : ''\">全部</text>\r\n\t\t\t<text v-for=\"(c,k) in cats\" @click=\"setCat(c.lx)\" :class=\"c.lx == cname ? 'act' : ''\">{{c.lx}}</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"box\" v-for=\"(c,k) in list\" @click=\"$api.tourl('/pages/news/show?id='+c.id)\">\r\n\t\t\t<text class=\"text1\">{{c.bt}}</text>\r\n\t\t\t<view class=\"sf\">\r\n\t\t\t\t<text class=\"bt\">{{c.lx}}</text>\r\n\t\t\t\t<view class=\"sft\">\r\n\t\t\t\t\t<view class=\"sdd\">\r\n\t\t\t\t\t\t<image src=\"/static/14.png\"></image>\r\n\t\t\t\t\t\t<text>{{c.lls}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"sdd\">\r\n\t\t\t\t\t\t<image src=\"/static/15.png\"></image>\r\n\t\t\t\t\t\t<text>{{c.optdt}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"cop\">{{nodata}}</view>\r\n\t\t<Footer :act=\"act\"></Footer>\n\t</view>\n</template>\n\n<script>\r\n\timport Footer from \"@/components/footer.vue\";\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tFooter\r\n\t\t},\n\t\tdata() {\n\t\t\treturn {\r\n\t\t\t\tcname:\"\",\r\n\t\t\t\tcats:[],\r\n\t\t\t\tlist:[],\r\n\t\t\t\tpage:1,\r\n\t\t\t\tact:\"ziliao\",\r\n\t\t\t\tck: 0,\r\n\t\t\t\tnodata:\"\",\r\n\t\t\t\tlx:\"\",\r\n\t\t\t\ttab:\"\",\n\t\t\t}\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\tconsole.log(e);\r\n\t\t\tif (e.lx == '资料') {\r\n\t\t\t\tthis.lx = ''\r\n\t\t\t}\r\n\t\t\t// this.lx = e.lx || \"\";\r\n\t\t\tthis.$api.msg(\"数据读取中...\");\r\n\t\t\tthis.$api.get('news/cats').then(res => {\r\n\t\t\t\tthis.cats = res;\r\n\t\t\t})\r\n\t\t\tthis.getData(this.lx);\r\n\t\t\t\r\n\t\t},\r\n\t\tonReachBottom(){\r\n\t\t\tif(this.nodata == \"\"){\r\n\t\t\t\tthis.page += 1;\r\n\t\t\t\tthis.$api.msg(\"数据读取中...\");\r\n\t\t\t\tthis.getData(this.cname);\r\n\t\t\t}\r\n\t\t},\r\n\t\t/*\r\n\t\t//上拉\r\n\t\tonReachBottom(){\r\n\t\t\tthis.page = this.page > 1 ? this.page - 1 : 1;\r\n\t\t\tthis.getData(this.cname);\r\n\t\t},\r\n\t\t//下拉\r\n\t\tonPullDownRefresh(){\r\n\t\t\tthis.page += 1; \r\n\t\t\tthis.getData(this.cname);\r\n\t\t},\r\n\t\t*/\n\t\tmethods: {\r\n\t\t\tsetCat(lx){\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.nodata = \"\";\r\n\t\t\t\tthis.cname = lx;\r\n\t\t\t\tif(lx == ''){\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle:'宁教通-学习资讯'\r\n\t\t\t\t\t})\r\n\t\t\t\t}else{\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle:'宁教通-'+lx\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tthis.getData(lx);\r\n\t\t\t\t// if(lx == '资料'){\r\n\t\t\t\t// \tthis.act = 'ziliao';\r\n\t\t\t\t// }else{\r\n\t\t\t\t// \tthis.act = '';\r\n\t\t\t\t// }\r\n\t\t\t},\r\n\t\t\tgetData(lx){\r\n\t\t\t\tthis.cname = lx;\r\n\t\t\t\t\r\n\t\t\t\tthis.$api.get('news/getList' , {\"lx\":lx,\"page\":this.page}).then(res => {\r\n\t\t\t\t\tif(!res || (res.length == 0)){\r\n\t\t\t\t\t\tthis.list = []\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthis.nodata = \"暂无更多数据\";\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.list = this.page == 1 ? res : this.list.concat(res);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" src=\"../ziliao/ziliao.scss\"></style>\n"], "sourceRoot": ""}