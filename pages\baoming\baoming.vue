<template>
	<view>
		<view class="navs">
			<text :class="'' == blx ? 'act' : ''" @click="clickCat('')">{{'全部分类'}}</text>
				
			<text v-for="(c,k) in baocats" :class="c.lx == blx ? 'act' : ''" @click="clickCat(c.lx)">{{c.lx}}</text>
		</view>
		<view class="giid">
			<view class="item" v-for="(c,k) in baolist">
				<image :src="c.fmtp" mode="" @click="tonbap(c.bmdz)"></image>
				<view class="slf">
					<text class="text1" @click="tonbap(c.bmdz)">{{c.bt}}</text>
					<text class="button" @click="tonbap(c.bmdz)">详情咨询</text>
				</view>
			</view>
		</view>
		<view class="cop">{{nodata}}</view>
		<view class="cop"></view>
	</view>
</template>

<script>
	import Footer from "@/components/footer.vue";
	export default {
		components: {
			Footer
		},
		data() {
			return {
				blx:"",
				baocats:[],
				nodata:"",
				page:1,
				baolist:[],
			}
		},
		onLoad() {
			this.$api.get('baoming/cats').then(res => {
				this.baocats = res;
			})
			this.setbCat('');
		},
		onReachBottom(){
			if(this.nodata == ""){
				this.page += 1;
				this.$api.msg("数据读取中...");
				this.setbCat(this.blx);
			}
		},
		/*
		//上拉
		onReachBottom(){
			this.page = this.page > 1 ? this.page - 1 : 1;
			this.setbCat(this.blx);
		},
		//下拉
		onPullDownRefresh(){
			this.page += 1; 
			this.setbCat(this.blx);
		},
		*/
		methods: {
			tonbap(url){
				location.href = url;
			},
			clickCat(lx){
				this.blx = lx;
				this.page = 1;
				this.baolist = [];
				this.setbCat(lx);
			},
			setbCat(lx){
				this.blx = lx;
				this.$api.get('baoming/getList', {
					'lx':lx,
					"page":this.page,
				}).then(res => {
					//uni.stopPullDownRefresh();
					//this.baolist = res;
					if(!res || (res.length == 0)){
						this.nodata = "暂无更多数据";
					}else{
						this.baolist = this.page == 1 ? res : this.baolist.concat(res);
					}
				})
			},
		}
	}
</script>

<style src="./baming.scss" lang="scss"></style>
