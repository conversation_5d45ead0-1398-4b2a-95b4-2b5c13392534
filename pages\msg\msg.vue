<template>
	<view style="padding: 20rpx; box-sizing: border-box;">
		<view class="sdd"></view>
		<view class="item" v-for="(c,k) in list" @click="$api.tourl('/pages/msg/show?id='+c.id)">
			<view class="stt">
				<image src="/static/wei.png" class="wei" v-if="c.ydzt == '2'"></image>
				<image src="/static/yi.png" class="wei" v-if="c.ydzt == '1'"></image>
				<text class="text1">{{c.bt}}</text>
			</view>
			<view class="sbb">
				<image src="/static/15.png" class="r"></image>
				<text>{{c.optdt}}</text>
			</view>
		</view>

		<view class="cop">{{nodata}}</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [],
				nodata: "",
				page: 1,
			}
		},
		onReachBottom() {
			if (this.nodata == "") {
				this.page += 1;
				this.$api.msg("数据读取中...");
				this.getData();
			}
		},
		onShow() {
			this.getData();
		},
		onLoad() {
			
		},
		methods: {
			getData() {
				this.$api.get('notice/getList', {
					page: this.page,
					openid: uni.getStorageSync('user').openid
				}).then(res => {
					if (!res || (res.length == 0)) {
						this.nodata = "暂无更多数据";
					} else {
						this.list = this.page == 1 ? res : this.list.concat(res);
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		font-family: 'PingFang';
		background-color: #fff;
	}

	.sdd {
		width: 100%;
		height: 8px;
		background: #F7F8FA;
		border-radius: 0px 0px 0px 0px;
	}

	.item {
		width: 100%;
		// margin-left: 2%;
		overflow: hidden;
		background: #F4F7FD;
		border-radius: 8px 8px 8px 8px;
		padding: 20rpx;
		box-sizing: border-box;

		margin-top: 30upx;

		.r {
			width: 40upx;
			height: 40upx;
		}

		.wei {
			width: 92upx;
			height: 48upx;

		}

		.stt {
			padding: 10rpx 20upx;
			display: flex;
			align-items: center;
		}

		.text1 {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 15px;
			color: #172030;
			text-align: left;
			font-style: normal;
			text-transform: none;
			padding-left: 10upx;
		}

		.sbb {
			padding: 10rpx 20upx;
			display: flex;
			align-items: center;

			text {
				padding-left: 15rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28upx;
				color: #999999;
			}
		}

		.slef {
			font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
			font-weight: 400;
			font-size: 12px;
			color: rgba(255, 255, 255, 0.95);
			line-height: 24px;
			background: #3984BC;
			border-radius: 10px 0px 10px 0px;
			padding: 5upx 10upx;
		}
	}
</style>