<template>
	<view>
		<view class="search-top">
			<text class="all" @click="setHide()">全部</text>
			<view class="sleft">
				<input type="text" v-model="keyword" placeholder="搜索课程名称"/>
				<text class="sub" @click="search()">搜索</text>
			</view>
		</view>
		<view class="navs">
			<text :class="'' == klx ? 'act' : ''" @click="clickCat('')">{{'全部'}}</text>
			
			<text :class="klx == c.lx ? 'act' : ''" v-for="(c,k) in kcat" @click="clickCat(c.lx)">{{c.lx}}</text>
			
		</view>
		<view class="box1" style="overflow: hidden;">
			<view class="item"  @click="$api.tourl('/pages/kecheng/show?id='+c.id)" v-for="(c,k) in kecheng">
				<image :src="c.fmtp" mode=""></image>
				<view class="text2">
					{{c.bt}}
				</view>
				<view class="firs">
					<view class="sdd">
						<text>{{c.ygm}}</text><text>人已购买</text>
					</view>
				</view>
				
				<view class="btn">
					<view class="bbb">免费试听</view>
				</view>
			</view>
			
		</view>
		<view class="cop">{{nodata || "没有更多数据了"}}</view>
		
		
		<!--弹窗-->
		<view class="mack" v-if="mark" @click="setHide()"></view>
		<view class="shimw" v-if="mark">
			<!-- <view class="stt">
				<text>{{cname}}</text>
				<u-icon name="arrow-right" color="#fff" size="12"></u-icon>
			</view> -->
			<view class="xxx">
				<view class="left">
					<view class="items">
						<text  @click="setC(null,-1)" :class="(gk == -1) ? 'act' : ''">全部</text>
						<text v-for="(c,k) in cats" @click="setC(c,k)" :class="(k == gk) ? 'act' : ''">{{c.group_name}}</text>
					</view>
					<view class="fb" @click="chongzhi()" style="display: none;">重置</view>
				</view>
				<view class="sright">
					<view class="box2">
						<block v-for="(c,k) in cats" v-if="gk != -1">
						<view class="item4"  v-if="k == gk">
							<view class="stt2" style="font-size: 12px;">{{c.group_name}}</view>
							<view class="ul">
								<text v-for="(c2,k2) in c.child"  @click="setchild(c2.title)" :class="c2.title == klx ? 'on' : ''">{{c2.title}}</text>
								
							</view>
						</view>
						</block>
						<block v-for="(c,k) in cats" v-if="gk == -1">
						<view class="item4">
							<view class="stt2" style="font-size: 12px;">{{c.group_name}}</view>
							<view class="ul">
								<text v-for="(c2,k2) in c.child"  @click="setchild(c2.title)" :class="c2.title == klx ? 'on' : ''">{{c2.title}}</text>
								
							</view>
						</view>
						</block>
					</view>
		
					<view class="butt" @click="butt()"><text>确定</text></view>
				</view>
			</view>
		</view>
		<!--弹窗-->
		<Footer :act="'kecheng'"></Footer>
	</view>
</template>

<script>
	import { msg } from "../../utils/request";
import Footer from "@/components/footer.vue";
	export default {
		components: {
			Footer
		},
		data() {
			return {
				klx:"",
				kcat:[],
				gk:-1,
				group_name:"",
				cats:[],
				mark:false,
				keyword:"",
				nodata:"",
				page:1,
				kecheng:[],
				
			}
		},
		onLoad() {
			//this.sekbCat('');
		
			this.$api.get('kecheng/getCatsGroup').then(res => {
				this.cats = res;
				//this.setHide();
				this.sekbCat('');
			})
			
		},
		/*
		//上拉
		onReachBottom(){
			this.page = this.page > 1 ? this.page - 1 : 1;
			this.sekbCat(this.klx);
		},
		//下拉
		onPullDownRefresh(){
			this.page += 1; 
			this.sekbCat(this.klx);
		},
		*/
		onReachBottom(){
			if(this.nodata == ""){
				this.page += 1;
				this.$api.msg("数据读取中...");
				this.sekbCat(this.klx);
			}
		},
		methods: {
			butt(){
				if(!this.klx){
					this.$api.msg("请选择分类");
					return false;
				}
				this.clickCat(this.klx);
				this.mark = false;
			},
			setchild(c){
				this.klx = c;
			},
			setC(c,k){
				if(c){
					this.group_name = c.group_name;
				}
				this.gk = k;
			},
			chongzhi(){
				
			},
			setHide(){
				this.mark = !this.mark;
			},
			clickCat(lx){
				this.klx = lx;
				this.page = 1;
				this.nodata = "";
				this.kecheng = [];
				this.sekbCat(lx);
			},
			search(){
				if(!this.keyword){
					//this.$api.msg("请输入关键词");
					//return false;
				}
				this.clickCat(this.klx);
			},
			sekbCat(lx){
				this.$api.msg("数据加载中...");
				this.klx = lx;
			
				this.$api.post('kecheng/getList', {
					'lx': lx,
					'limit': 10,
					'keyword':this.keyword,
					"page":this.page,
				}).then(res => {
					if(!res || (res.length == 0)){
						this.nodata = "暂无更多数据";
					}else{
						this.kecheng = this.page == 1 ? res : this.kecheng.concat(res);
					}
				})
			},
		}
	}
</script>

<style src="./kecheng.scss" lang="scss"></style>
