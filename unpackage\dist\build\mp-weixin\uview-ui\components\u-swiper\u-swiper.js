(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uview-ui/components/u-swiper/u-swiper"],{"0f41":function(e,t,i){},6971:function(e,t,i){"use strict";i.r(t);var n=i("c4a5"),u=i("ae44");for(var r in u)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return u[e]}))}(r);i("6e36");var o=i("828b"),a=Object(o["a"])(u["default"],n["b"],n["c"],!1,null,"9c9efd1c",null,!1,n["a"],void 0);t["default"]=a.exports},"6e36":function(e,t,i){"use strict";var n=i("0f41"),u=i.n(n);u.a},ae44:function(e,t,i){"use strict";i.r(t);var n=i("ccf2"),u=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=u.a},c4a5:function(e,t,i){"use strict";i.d(t,"b",(function(){return u})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return n}));var n={uLoadingIcon:function(){return Promise.all([i.e("common/vendor"),i.e("uview-ui/components/u-loading-icon/u-loading-icon")]).then(i.bind(null,"1200"))},uSwiperIndicator:function(){return Promise.all([i.e("common/vendor"),i.e("uview-ui/components/u-swiper-indicator/u-swiper-indicator")]).then(i.bind(null,"377f"))}},u=function(){var e=this,t=e.$createElement,i=(e._self._c,e.$u.addUnit(e.height)),n=e.$u.addUnit(e.radius),u=e.loading?null:e.$u.addUnit(e.height),r=e.loading?null:e.$u.addUnit(e.previousMargin),o=e.loading?null:e.$u.addUnit(e.nextMargin),a=e.loading?null:e.__map(e.list,(function(t,i){var n=e.__get_orig(t),u=e.__get_style([e.itemStyle(i)]),r=e.getItemType(t),o="image"===r?e.$u.addUnit(e.height):null,a="image"===r?e.$u.addUnit(e.radius):null,d="image"===r?e.getSource(t):null,c=e.getItemType(t),l="video"===c?e.$u.addUnit(e.height):null,s="video"===c?e.getSource(t):null,g="video"===c?e.getPoster(t):null,f="video"===c?e.showTitle&&e.$u.test.object(t)&&t.title:null,m=e.showTitle&&e.$u.test.object(t)&&t.title&&e.$u.test.image(e.getSource(t));return{$orig:n,s0:u,m0:r,g5:o,g6:a,m1:d,m2:c,g7:l,m3:s,m4:g,g8:f,g9:m}})),d=e.__get_style([e.$u.addStyle(e.indicatorStyle)]),c=e.loading||!e.indicator||e.showTitle?null:e.list.length;e.$mp.data=Object.assign({},{$root:{g0:i,g1:n,g2:u,g3:r,g4:o,l0:a,s1:d,g10:c}})},r=[]},ccf2:function(e,t,i){"use strict";(function(e){var n=i("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var u=n(i("3b2d")),r=n(i("72fd")),o={name:"u-swiper",mixins:[e.$u.mpMixin,e.$u.mixin,r.default],data:function(){return{currentIndex:0}},watch:{current:function(e,t){e!==t&&(this.currentIndex=e)}},computed:{itemStyle:function(){var t=this;return function(i){var n={};return t.nextMargin&&t.previousMargin&&(n.borderRadius=e.$u.addUnit(t.radius),i!==t.currentIndex&&(n.transform="scale(0.92)")),n}}},methods:{getItemType:function(t){return"string"===typeof t?e.$u.test.video(this.getSource(t))?"video":"image":"object"===(0,u.default)(t)&&this.keyName?t.type?"image"===t.type?"image":"video"===t.type?"video":"image":e.$u.test.video(this.getSource(t))?"video":"image":void 0},getSource:function(t){return"string"===typeof t?t:"object"===(0,u.default)(t)&&this.keyName?t[this.keyName]:(e.$u.error("请按格式传递列表参数"),"")},change:function(e){var t=e.detail.current;this.pauseVideo(this.currentIndex),this.currentIndex=t,this.$emit("change",e.detail)},pauseVideo:function(t){var i=this.getSource(this.list[t]);if(e.$u.test.video(i)){var n=e.createVideoContext("video-".concat(t),this);n.pause()}},getPoster:function(e){return"object"===(0,u.default)(e)&&e.poster?e.poster:""},clickHandler:function(e){this.$emit("click",e)}}};t.default=o}).call(this,i("df3c")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uview-ui/components/u-swiper/u-swiper-create-component',
    {
        'uview-ui/components/u-swiper/u-swiper-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("6971"))
        })
    },
    [['uview-ui/components/u-swiper/u-swiper-create-component']]
]);
